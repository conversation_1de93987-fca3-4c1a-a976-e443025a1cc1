# 🪟 Ukuqala Events - Windows Setup Guide

This guide is specifically designed for Windows users to set up the Ukuqala Events application.

## 🎯 Quick Start for Windows

### Option 1: Automated Setup (Easiest)
1. **Download** the project files
2. **Extract** to a folder like `C:\ukuqala-events\`
3. **Double-click** `quick-start.bat`
4. **Follow** the prompts

### Option 2: XAMPP Setup (Alternative)
If you prefer using XAMPP instead of Docker.

## 📋 Prerequisites for Windows

### Required Software
Choose **ONE** of these options:

#### Option A: Docker Desktop (Recommended)
- ✅ **Docker Desktop for Windows** (includes Docker Compose)
- ✅ **PHP 8.0+** (standalone or via XAMPP)
- ✅ **WSL2** (for better Docker performance)

#### Option B: XAMPP Stack (Alternative)
- ✅ **XAMPP 8.0+** (includes PHP, MySQL, Apache)
- ✅ **Web browser** (Chrome, Firefox, Edge)

### System Requirements
- **Windows 10/11** (64-bit)
- **4GB RAM** (minimum 2GB)
- **2GB free disk space**
- **Internet connection** (for initial setup)

## 🚀 Method 1: Docker Desktop Setup

### Step 1: Install Docker Desktop
1. **Download**: https://docs.docker.com/desktop/windows/
2. **Install** Docker Desktop
3. **Enable WSL2** when prompted
4. **Restart** your computer
5. **Start** Docker Desktop

### Step 2: Install PHP (if not already installed)
**Option A: Download PHP directly**
1. Go to: https://windows.php.net/download/
2. Download **PHP 8.0+ Thread Safe** version
3. Extract to `C:\php\`
4. Add `C:\php\` to your PATH environment variable

**Option B: Use XAMPP for PHP only**
1. Download XAMPP: https://www.apachefriends.org/
2. Install XAMPP (you'll only use PHP from it)
3. Add `C:\xampp\php\` to your PATH

### Step 3: Run the Setup Script
```cmd
# Open Command Prompt or PowerShell
cd C:\path\to\ukuqala-events\

# Run the automated setup
quick-start.bat
```

### Step 4: Access the Application
- **Application**: http://localhost:8000
- **Admin Panel**: http://localhost:8000/admin/
- **phpMyAdmin**: http://localhost:8080

## 🔧 Method 2: XAMPP Setup

### Step 1: Install XAMPP
1. **Download**: https://www.apachefriends.org/download.html
2. **Install** XAMPP with PHP 8.0+ and MySQL
3. **Start** XAMPP Control Panel
4. **Start** Apache and MySQL services

### Step 2: Setup the Project
```cmd
# Copy project files to XAMPP htdocs
copy /E ukuqala-events C:\xampp\htdocs\

# Or extract directly to C:\xampp\htdocs\ukuqala-events\
```

### Step 3: Create Database
1. **Open**: http://localhost/phpmyadmin
2. **Create** new database: `ukuqala_events`
3. **Import** the file: `database/schema.sql`

### Step 4: Configure Application
Edit `includes/config.php`:
```php
// XAMPP Configuration
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'ukuqala_events');
define('DB_USER', 'root');
define('DB_PASS', ''); // Empty for default XAMPP
```

### Step 5: Access Application
- **Application**: http://localhost/ukuqala-events/
- **Admin Panel**: http://localhost/ukuqala-events/admin/
- **phpMyAdmin**: http://localhost/phpmyadmin

## 🛠️ Windows-Specific Commands

### PowerShell Commands
```powershell
# Check if Docker is installed
docker --version
docker-compose --version

# Check if PHP is installed
php --version

# Check PHP extensions
php -m

# Start PHP development server
php -S localhost:8000

# Check what's using a port
netstat -an | findstr :8000
```

### Command Prompt Commands
```cmd
# Navigate to project directory
cd C:\ukuqala-events\

# Create directories
mkdir uploads
mkdir logs

# Check services
tasklist | findstr mysql
tasklist | findstr apache
```

## 🔧 Common Windows Issues & Solutions

### Issue 1: Docker Desktop Won't Start
**Solution:**
```powershell
# Enable WSL2 (run as Administrator)
wsl --install
wsl --set-default-version 2

# Enable Hyper-V (Windows 10 Pro/Enterprise)
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
```

### Issue 2: Port 80 Already in Use (XAMPP)
**Solution:**
1. Open XAMPP Control Panel
2. Click **Config** next to Apache
3. Select **httpd.conf**
4. Change `Listen 80` to `Listen 8080`
5. Save and restart Apache
6. Access via: http://localhost:8080/ukuqala-events/

### Issue 3: MySQL Port 3306 in Use
**Solution:**
```cmd
# Stop Windows MySQL service
net stop mysql

# Or change XAMPP MySQL port
# Edit C:\xampp\mysql\bin\my.ini
# Change: port=3306 to port=3307
```

### Issue 4: PHP Extensions Missing
**Solution:**
1. Open `php.ini` file
2. Uncomment these lines (remove `;`):
```ini
extension=mysqli
extension=curl
extension=mbstring
extension=openssl
extension=json
```
3. Restart web server

### Issue 5: Permission Denied Errors
**Solution:**
```cmd
# Run Command Prompt as Administrator
# Or move project to a folder you have write access to
# Example: C:\Users\<USER>\Documents\ukuqala-events\
```

## 📁 Windows File Structure
```
C:\ukuqala-events\
├── admin\
│   ├── index.php
│   ├── events.php
│   └── reports.php
├── assets\
│   ├── css\
│   ├── js\
│   └── images\
├── auth\
├── booking\
├── database\
│   └── schema.sql
├── events\
├── includes\
│   └── config.php
├── user\
├── index.php
├── quick-start.bat
├── docker-compose.yml
└── WINDOWS_SETUP.md
```

## 🎯 Testing Your Setup

### 1. Test Database Connection
1. Open: http://localhost:8000 (or http://localhost/ukuqala-events/)
2. You should see the homepage without errors

### 2. Test Admin Access
1. Go to: http://localhost:8000/admin/
2. Login with: `<EMAIL>` / `password`
3. You should see the admin dashboard

### 3. Test User Registration
1. Click "Register" on the homepage
2. Create a new user account
3. Login and access user dashboard

## 🔑 Default Access Information

**Application URLs:**
- Homepage: http://localhost:8000 (Docker) or http://localhost/ukuqala-events/ (XAMPP)
- Admin Panel: Add `/admin/` to the above URL
- phpMyAdmin: http://localhost:8080 (Docker) or http://localhost/phpmyadmin (XAMPP)

**Admin Credentials:**
- Email: `<EMAIL>`
- Password: `password`

**Database (Docker):**
- Host: `localhost:3306`
- Database: `ukuqala_events`
- Username: `ukuqala_user`
- Password: `ukuqala_pass123`

**Database (XAMPP):**
- Host: `localhost:3306`
- Database: `ukuqala_events`
- Username: `root`
- Password: (empty)

## 🆘 Getting Help

If you encounter issues:

1. **Check the main setup guide**: [SETUP_GUIDE.md](SETUP_GUIDE.md)
2. **Review error messages** carefully
3. **Check Windows Event Viewer** for system errors
4. **Restart services** (Docker Desktop, XAMPP, etc.)
5. **Run as Administrator** if you get permission errors

## 🎉 Success!

Once setup is complete, you'll have:
- ✅ A fully functional event booking system
- ✅ Modern Spotify-inspired dark theme
- ✅ Admin panel for event management
- ✅ User registration and booking system
- ✅ Database with sample events

**Enjoy using Ukuqala Events!** 🎊
