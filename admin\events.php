<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Manage Events';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add':
            $eventData = [
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'event_date' => $_POST['event_date'],
                'event_time' => $_POST['event_time'],
                'venue' => trim($_POST['venue']),
                'location' => trim($_POST['location']),
                'organizer' => trim($_POST['organizer']),
                'organizer_contact' => trim($_POST['organizer_contact']),
                'image_url' => trim($_POST['image_url']),
                'price' => (float)$_POST['price'],
                'total_tickets' => (int)$_POST['total_tickets'],
                'category' => trim($_POST['category'])
            ];

            if ($eventManager->addEvent($eventData)) {
                setFlashMessage('success', 'Event added successfully!');
            } else {
                setFlashMessage('error', 'Failed to add event.');
            }
            break;

        case 'edit':
            $eventId = (int)$_POST['event_id'];
            $eventData = [
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'event_date' => $_POST['event_date'],
                'event_time' => $_POST['event_time'],
                'venue' => trim($_POST['venue']),
                'location' => trim($_POST['location']),
                'organizer' => trim($_POST['organizer']),
                'organizer_contact' => trim($_POST['organizer_contact']),
                'image_url' => trim($_POST['image_url']),
                'price' => (float)$_POST['price'],
                'total_tickets' => (int)$_POST['total_tickets'],
                'category' => trim($_POST['category']),
                'status' => $_POST['status']
            ];

            if ($eventManager->updateEvent($eventId, $eventData)) {
                setFlashMessage('success', 'Event updated successfully!');
            } else {
                setFlashMessage('error', 'Failed to update event.');
            }
            break;

        case 'delete':
            $eventId = (int)$_POST['event_id'];
            if ($eventManager->deleteEvent($eventId)) {
                setFlashMessage('success', 'Event deleted successfully!');
            } else {
                setFlashMessage('error', 'Failed to delete event.');
            }
            break;

        case 'toggle_status':
            $eventId = (int)$_POST['event_id'];
            $currentStatus = $_POST['current_status'];
            $newStatus = $currentStatus === 'active' ? 'cancelled' : 'active';

            if ($eventManager->updateEventStatus($eventId, $newStatus)) {
                setFlashMessage('success', 'Event status updated successfully!');
            } else {
                setFlashMessage('error', 'Failed to update event status.');
            }
            break;
    }

    redirect('events.php');
}

// Get filters
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$status = $_GET['status'] ?? '';

// Get events with filters
if (!empty($search) || !empty($category) || !empty($status)) {
    $events = $eventManager->searchEvents($search, $category, '', '', $status);
} else {
    $events = $eventManager->getAllEvents();
}

$flashMessage = getFlashMessage();

// Get categories for filter
$categories = ['Concert', 'Conference', 'Workshop', 'Entertainment', 'Sports', 'Education', 'Business', 'Other'];

// Get event for editing if requested
$editEvent = null;
if (isset($_GET['edit'])) {
    $editEvent = $eventManager->getEventById($_GET['edit']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .main-content {
            padding-top: 100px;
            padding-bottom: 60px;
        }

        .filter-card {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--bg-elevated);
            box-shadow: var(--shadow-soft);
            margin-bottom: 2rem;
            color: var(--text-primary);
        }

        .filter-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .event-card-admin {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-soft);
            transition: all 0.2s ease;
            border: 1px solid var(--bg-elevated);
            overflow: hidden;
            color: var(--text-primary);
        }

        .event-card-admin:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-spotify);
            border-color: var(--primary-color);
        }

        .event-status-badge {
            position: absolute;
            top: var(--space-md);
            right: var(--space-md);
            z-index: 2;
        }

        .status-active {
            background: rgba(29, 185, 84, 0.2);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .status-cancelled {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }

        .status-draft {
            background: rgba(107, 114, 128, 0.2);
            color: var(--text-secondary);
            border: 1px solid var(--text-secondary);
        }

        .event-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .event-actions {
            display: flex;
            gap: var(--space-xs);
            flex-wrap: wrap;
        }

        .stats-card {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-soft);
            border: 1px solid var(--bg-elevated);
            color: var(--text-primary);
        }

        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: var(--space-md);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                Ukuqala Events Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="events.php">Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="bookings.php">Bookings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">Reports</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../">
                            <i class="fas fa-globe me-1"></i>View Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">User Dashboard</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h2 fw-bold mb-2">
                                <i class="fas fa-calendar-alt me-3 text-primary"></i>
                                Event Management
                            </h1>
                            <p class="text-muted mb-0">Create, edit, and manage your events with ease</p>
                        </div>
                        <button class="btn btn-primary-modern btn-lg" data-bs-toggle="modal" data-bs-target="#addEventModal">
                            <i class="fas fa-plus me-2"></i>Add New Event
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="filter-card p-4">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search Events</label>
                                <input type="text" class="form-control form-control-modern" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by title, venue...">
                            </div>
                            <div class="col-md-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-control form-control-modern" id="category" name="category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo $cat; ?>" <?php echo $category === $cat ? 'selected' : ''; ?>>
                                            <?php echo $cat; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-control form-control-modern" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                    <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary-enhanced w-100">
                                    <i class="fas fa-search me-2"></i>Filter
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <?php if ($flashMessage): ?>
                <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                    <?php echo $flashMessage['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Events Grid -->
            <?php if (!empty($events)): ?>
                <div class="row">
                    <?php foreach ($events as $event): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="event-card-admin">
                                <div class="position-relative">
                                    <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>"
                                         alt="<?php echo htmlspecialchars($event->title); ?>"
                                         class="event-image">

                                    <div class="event-status-badge">
                                        <span class="badge status-<?php echo $event->status; ?> px-3 py-2">
                                            <?php echo ucfirst($event->status); ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="p-4">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h5 class="fw-bold mb-1"><?php echo htmlspecialchars($event->title); ?></h5>
                                            <span class="badge bg-light text-dark"><?php echo htmlspecialchars($event->category); ?></span>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-primary"><?php echo formatCurrency($event->price); ?></div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-calendar-alt text-muted me-2"></i>
                                            <small><?php echo formatDate($event->event_date); ?> at <?php echo formatTime($event->event_time); ?></small>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                            <small><?php echo htmlspecialchars($event->venue . ', ' . $event->location); ?></small>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-ticket-alt text-muted me-2"></i>
                                            <small>
                                                <span class="fw-bold"><?php echo $event->available_tickets; ?></span> / <?php echo $event->total_tickets; ?> tickets available
                                            </small>
                                        </div>
                                    </div>

                                    <p class="text-muted small mb-3">
                                        <?php echo htmlspecialchars(substr($event->description, 0, 100)) . '...'; ?>
                                    </p>

                                    <div class="event-actions">
                                        <a href="../events/details.php?id=<?php echo $event->id; ?>"
                                           class="btn btn-outline-primary btn-sm" target="_blank">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                        <button class="btn btn-outline-warning btn-sm"
                                                onclick="editEvent(<?php echo $event->id; ?>)">
                                            <i class="fas fa-edit me-1"></i>Edit
                                        </button>
                                        <?php if ($event->status === 'active'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="event_id" value="<?php echo $event->id; ?>">
                                                <button type="submit" class="btn btn-outline-danger btn-sm"
                                                        onclick="return confirm('Are you sure you want to cancel this event?')">
                                                    <i class="fas fa-ban me-1"></i>Cancel
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-calendar-times fa-5x text-muted"></i>
                            </div>
                            <h3 class="fw-bold mb-3">No Events Found</h3>
                            <p class="text-muted mb-4">
                                <?php if (!empty($search) || !empty($category) || !empty($status)): ?>
                                    No events match your current filters. Try adjusting your search criteria.
                                <?php else: ?>
                                    Start by creating your first event to get your platform up and running.
                                <?php endif; ?>
                            </p>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <button class="btn btn-primary-enhanced btn-lg" data-bs-toggle="modal" data-bs-target="#addEventModal">
                                    <i class="fas fa-plus me-2"></i>Add New Event
                                </button>
                                <?php if (!empty($search) || !empty($category) || !empty($status)): ?>
                                    <a href="events.php" class="btn btn-secondary-enhanced btn-lg">
                                        <i class="fas fa-times me-2"></i>Clear Filters
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Add Event Modal -->
    <div class="modal fade" id="addEventModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="title" class="form-label">Event Title *</label>
                                <input type="text" class="form-control form-control-modern" id="title" name="title" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-control form-control-modern" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="Technology">Technology</option>
                                    <option value="Music">Music</option>
                                    <option value="Business">Business</option>
                                    <option value="Art">Art</option>
                                    <option value="Sports">Sports</option>
                                    <option value="Education">Education</option>
                                    <option value="Food">Food</option>
                                    <option value="Health">Health</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control form-control-modern" id="description" name="description" rows="4" required></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="event_date" class="form-label">Event Date *</label>
                                <input type="date" class="form-control form-control-modern" id="event_date" name="event_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="event_time" class="form-label">Event Time *</label>
                                <input type="time" class="form-control form-control-modern" id="event_time" name="event_time" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="venue" class="form-label">Venue *</label>
                                <input type="text" class="form-control form-control-modern" id="venue" name="venue" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="location" class="form-label">City/Location *</label>
                                <input type="text" class="form-control form-control-modern" id="location" name="location" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="organizer" class="form-label">Organizer *</label>
                                <input type="text" class="form-control form-control-modern" id="organizer" name="organizer" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="organizer_contact" class="form-label">Organizer Contact *</label>
                                <input type="text" class="form-control form-control-modern" id="organizer_contact" name="organizer_contact" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Price (<?php echo APP_CURRENCY_SYMBOL; ?>) *</label>
                                <input type="number" class="form-control form-control-modern" id="price" name="price" min="0" step="100" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="total_tickets" class="form-label">Total Tickets *</label>
                                <input type="number" class="form-control form-control-modern" id="total_tickets" name="total_tickets" min="1" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="image_url" class="form-label">Image URL</label>
                                <input type="url" class="form-control form-control-modern" id="image_url" name="image_url"
                                       placeholder="https://example.com/image.jpg">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary-modern">
                            <i class="fas fa-save me-2"></i>Create Event
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        // Set minimum date to today
        document.getElementById('event_date').min = new Date().toISOString().split('T')[0];

        function editEvent(eventId) {
            // This would open an edit modal with event data
            alert('Edit functionality would be implemented here for event ID: ' + eventId);
        }
    </script>
</body>
</html>
