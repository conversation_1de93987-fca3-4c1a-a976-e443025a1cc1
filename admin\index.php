<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$pageTitle = 'Admin Dashboard';

// Get dashboard statistics
$totalEvents = $eventManager->getTotalEventsCount();
$totalUsers = $userManager->getTotalUsersCount();
$totalBookings = $bookingManager->getTotalBookingsCount();

// Get recent bookings
$db->query('SELECT b.*, e.title, u.first_name, u.last_name
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           JOIN users u ON b.user_id = u.id
           ORDER BY b.created_at DESC
           LIMIT 5');
$recentBookings = $db->resultset();

// Get revenue statistics
$db->query('SELECT SUM(total_amount) as total_revenue FROM bookings WHERE booking_status = "confirmed"');
$revenueResult = $db->single();
$totalRevenue = $revenueResult ? $revenueResult->total_revenue : 0;

// Get monthly revenue for chart
$db->query('SELECT
    DATE_FORMAT(created_at, "%Y-%m") as month,
    SUM(total_amount) as revenue,
    COUNT(*) as bookings
    FROM bookings
    WHERE booking_status = "confirmed"
    AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, "%Y-%m")
    ORDER BY month DESC
    LIMIT 12');
$monthlyStats = $db->resultset();

// Get event categories statistics
$db->query('SELECT
    e.category,
    COUNT(*) as event_count,
    SUM(b.quantity) as tickets_sold,
    SUM(b.total_amount) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id AND b.booking_status = "confirmed"
    WHERE e.status = "active"
    GROUP BY e.category
    ORDER BY revenue DESC');
$categoryStats = $db->resultset();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Ukuqala Events</title>

    <!-- African-Inspired Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Playfair+Display:wght@400;500;600;700;800&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--bg-primary);
            min-height: 100vh;
            color: var(--text-primary);
        }

        .main-content {
            padding-top: 100px;
            padding-bottom: 60px;
        }

        .admin-welcome {
            background: var(--bg-secondary);
            color: var(--text-primary);
            padding: 3rem 0 2rem;
            margin-bottom: 2rem;
            border-radius: var(--border-radius-lg);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-spotify);
            border: 1px solid var(--bg-elevated);
        }

        .admin-welcome::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(29, 185, 84, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .admin-welcome .container-fluid {
            position: relative;
            z-index: 2;
        }

        .welcome-stats {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--bg-elevated);
            padding: 1.5rem;
            margin-top: 1.5rem;
            box-shadow: var(--shadow-soft);
        }

        .admin-stats-card {
            background: var(--bg-tertiary);
            border: 1px solid var(--bg-elevated);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            box-shadow: var(--shadow-soft);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            height: 100%;
            color: var(--text-primary);
        }

        .admin-stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
        }

        .admin-stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-spotify);
            border-color: var(--primary-color);
            background: var(--bg-elevated);
        }

        .admin-stat-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius-round);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-soft);
        }

        .admin-stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            font-family: var(--font-display);
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .admin-stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .chart-card {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-soft);
            border: 1px solid var(--bg-elevated);
            overflow: hidden;
            color: var(--text-primary);
        }

        .chart-header {
            background: var(--bg-elevated);
            color: var(--text-primary);
            padding: 1.5rem;
            position: relative;
            border-bottom: 1px solid var(--bg-elevated);
        }

        .category-stat {
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 3px solid var(--primary-color);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            color: var(--text-primary);
        }

        .category-stat:hover {
            transform: translateX(4px);
            box-shadow: var(--shadow-soft);
            border-left-color: var(--primary-light);
            background: var(--bg-elevated);
        }

        .recent-bookings-card {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-soft);
            border: 1px solid var(--bg-elevated);
            overflow: hidden;
            color: var(--text-primary);
        }

        .table-modern {
            margin-bottom: 0;
            color: var(--text-primary);
        }

        .table-modern thead th {
            background: var(--bg-elevated);
            border: none;
            font-weight: 600;
            color: var(--text-primary);
            padding: 1rem;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.5px;
        }

        .table-modern tbody td {
            padding: 1rem;
            border-color: var(--bg-elevated);
            vertical-align: middle;
            color: var(--text-secondary);
        }

        .table-modern tbody tr:hover {
            background: var(--bg-elevated);
        }

        .quick-actions-card {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            box-shadow: var(--shadow-soft);
            border: 1px solid var(--bg-elevated);
        }

        .action-btn {
            background: var(--bg-elevated);
            border: 1px solid var(--bg-elevated);
            color: var(--text-primary);
            border-radius: var(--border-radius);
            padding: 1rem;
            transition: all 0.2s ease;
            text-decoration: none;
            display: block;
            text-align: center;
        }

        .action-btn:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-soft);
            border-color: var(--primary-color);
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            top: 10%;
            right: 10%;
            animation-delay: 0s;
        }

        .floating-elements::after {
            bottom: 10%;
            left: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
    </style>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../">
                <i class="fas fa-drum me-2"></i>
                Ukuqala Events Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="fas fa-calendar-star me-2"></i>Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="bookings.php">
                            <i class="fas fa-ticket-alt me-2"></i>Bookings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-line me-2"></i>Reports
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../">
                            <i class="fas fa-globe me-2"></i>View Site
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-2"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">
                                <i class="fas fa-user me-2"></i>User Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">
                                <i class="fas fa-user-edit me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="../user/bookings.php">
                                <i class="fas fa-ticket-alt me-2"></i>My Bookings
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Admin Welcome Section -->
    <section class="admin-welcome">
        <div class="floating-elements"></div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-crown fa-4x mb-3" style="color: rgba(255,255,255,0.8);"></i>
                        </div>
                        <h1 class="display-4 fw-bold mb-3">
                            Admin Command Center
                        </h1>
                        <p class="lead mb-4">Welcome back, <?php echo htmlspecialchars($_SESSION['first_name']); ?>! Your Ukuqala Events empire awaits.</p>

                        <div class="welcome-stats">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="h3 fw-bold mb-1"><?php echo number_format($totalEvents); ?></div>
                                    <div class="small opacity-75">Active Events</div>
                                </div>
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="h3 fw-bold mb-1"><?php echo number_format($totalUsers); ?></div>
                                    <div class="small opacity-75">Happy Users</div>
                                </div>
                                <div class="col-md-3 mb-3 mb-md-0">
                                    <div class="h3 fw-bold mb-1"><?php echo number_format($totalBookings); ?></div>
                                    <div class="small opacity-75">Total Bookings</div>
                                </div>
                                <div class="col-md-3">
                                    <div class="h3 fw-bold mb-1"><?php echo formatCurrency($totalRevenue); ?></div>
                                    <div class="small opacity-75">Revenue Generated</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">

            <!-- Enhanced Statistics Cards -->
            <div class="row mb-5">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="admin-stats-card text-center">
                        <div class="admin-stat-icon bg-primary text-white">
                            <i class="fas fa-calendar-star"></i>
                        </div>
                        <div class="admin-stat-number"><?php echo number_format($totalEvents); ?></div>
                        <div class="admin-stat-label">Active Events</div>
                        <div class="mt-3">
                            <a href="events.php" class="btn btn-primary-modern btn-sm">
                                <i class="fas fa-eye me-1"></i>View All
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="admin-stats-card text-center">
                        <div class="admin-stat-icon bg-success text-white">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="admin-stat-number"><?php echo number_format($totalUsers); ?></div>
                        <div class="admin-stat-label">Ubuntu Community</div>
                        <div class="mt-3">
                            <span class="badge bg-success fs-6">Growing Daily</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="admin-stats-card text-center">
                        <div class="admin-stat-icon bg-info text-white">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="admin-stat-number"><?php echo number_format($totalBookings); ?></div>
                        <div class="admin-stat-label">Total Bookings</div>
                        <div class="mt-3">
                            <a href="bookings.php" class="btn btn-accent-modern btn-sm">
                                <i class="fas fa-list me-1"></i>Manage
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="admin-stats-card text-center">
                        <div class="admin-stat-icon bg-warning text-white">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="admin-stat-number"><?php echo formatCurrency($totalRevenue); ?></div>
                        <div class="admin-stat-label">Total Revenue</div>
                        <div class="mt-3">
                            <a href="reports.php" class="btn btn-secondary-modern btn-sm">
                                <i class="fas fa-chart-line me-1"></i>Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-5">
                <!-- Revenue Chart -->
                <div class="col-lg-8 mb-4">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-chart-line me-2"></i>
                                Monthly Revenue Trends
                            </h5>
                            <small class="opacity-75">Track your business growth over time</small>
                        </div>
                        <div class="p-4">
                            <canvas id="revenueChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Category Statistics -->
                <div class="col-lg-4 mb-4">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-chart-pie me-2"></i>
                                Event Categories
                            </h5>
                            <small class="opacity-75">Performance by category</small>
                        </div>
                        <div class="p-4">
                            <?php if (!empty($categoryStats)): ?>
                                <?php foreach ($categoryStats as $index => $category): ?>
                                    <div class="category-stat">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <span class="badge bg-primary rounded-pill">#<?php echo $index + 1; ?></span>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($category->category); ?></div>
                                                    <small class="text-muted"><?php echo $category->event_count; ?> events</small>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <div class="fw-bold text-primary"><?php echo formatCurrency($category->revenue ?: 0); ?></div>
                                                <small class="text-muted"><?php echo $category->tickets_sold ?: 0; ?> tickets</small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No category data available</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="recent-bookings-card">
                        <div class="chart-header d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-0 fw-bold">
                                    <i class="fas fa-clock me-2"></i>
                                    Recent Bookings
                                </h5>
                                <small class="opacity-75">Latest customer activity</small>
                            </div>
                            <a href="bookings.php" class="btn btn-light btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>
                                View All Bookings
                            </a>
                        </div>
                        <div class="p-0">
                            <?php if (!empty($recentBookings)): ?>
                                <div class="table-responsive">
                                    <table class="table table-modern">
                                        <thead>
                                            <tr>
                                                <th>Reference</th>
                                                <th>Customer</th>
                                                <th>Event</th>
                                                <th>Quantity</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentBookings as $booking): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-primary fs-6"><?php echo $booking->booking_reference; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($booking->first_name . ' ' . $booking->last_name); ?></div>
                                                    </td>
                                                    <td>
                                                        <div class="fw-bold"><?php echo htmlspecialchars($booking->title); ?></div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info"><?php echo $booking->quantity; ?> ticket<?php echo $booking->quantity > 1 ? 's' : ''; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="fw-bold text-success"><?php echo formatCurrency($booking->total_amount); ?></div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $booking->booking_status === 'confirmed' ? 'success' : 'warning'; ?> fs-6">
                                                            <i class="fas fa-<?php echo $booking->booking_status === 'confirmed' ? 'check-circle' : 'clock'; ?> me-1"></i>
                                                            <?php echo ucfirst($booking->booking_status); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="text-muted"><?php echo formatDate($booking->created_at); ?></div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h5>No Recent Bookings</h5>
                                    <p class="text-muted">Bookings will appear here as customers make reservations</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="quick-actions-card">
                        <div class="text-center mb-4">
                            <h5 class="mb-2 fw-bold">
                                <i class="fas fa-rocket me-2"></i>
                                Quick Actions
                            </h5>
                            <p class="mb-0 opacity-75">Manage your events platform efficiently</p>
                        </div>
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="events.php?action=add" class="action-btn">
                                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                    <div class="fw-bold">Add New Event</div>
                                    <small class="opacity-75">Create exciting events</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="bookings.php" class="action-btn">
                                    <i class="fas fa-list-alt fa-2x mb-2"></i>
                                    <div class="fw-bold">Manage Bookings</div>
                                    <small class="opacity-75">Handle reservations</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="reports.php" class="action-btn">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                    <div class="fw-bold">View Reports</div>
                                    <small class="opacity-75">Analyze performance</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="../" class="action-btn">
                                    <i class="fas fa-globe fa-2x mb-2"></i>
                                    <div class="fw-bold">View Website</div>
                                    <small class="opacity-75">See customer view</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <!-- Chart.js Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Revenue Chart
            const ctx = document.getElementById('revenueChart').getContext('2d');
            const monthlyData = <?php echo json_encode(array_reverse($monthlyStats)); ?>;

            const labels = monthlyData.map(item => {
                const date = new Date(item.month + '-01');
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            });

            const revenues = monthlyData.map(item => parseFloat(item.revenue) || 0);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Revenue (<?php echo APP_CURRENCY_SYMBOL; ?>)',
                        data: revenues,
                        borderColor: '#1db954',
                        backgroundColor: 'rgba(29, 185, 84, 0.15)',
                        tension: 0.4,
                        fill: true,
                        borderWidth: 4,
                        pointBackgroundColor: '#1db954',
                        pointBorderColor: '#282828',
                        pointBorderWidth: 3,
                        pointRadius: 8,
                        pointHoverRadius: 12,
                        pointHoverBackgroundColor: '#1ed760',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff',
                                font: {
                                    family: 'Circular, Helvetica Neue, Arial, sans-serif',
                                    size: 14,
                                    weight: '500'
                                },
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            backgroundColor: '#191414',
                            titleColor: '#ffffff',
                            bodyColor: '#b3b3b3',
                            borderColor: '#1db954',
                            borderWidth: 2,
                            cornerRadius: 8,
                            padding: 12,
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 13
                            },
                            callbacks: {
                                label: function(context) {
                                    return 'Revenue: ' + new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' <?php echo APP_CURRENCY_SYMBOL; ?>';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: '#404040',
                                borderColor: '#535353',
                                lineWidth: 1
                            },
                            ticks: {
                                color: '#b3b3b3',
                                font: {
                                    family: 'Circular, Helvetica Neue, Arial, sans-serif',
                                    size: 12
                                },
                                padding: 10
                            },
                            border: {
                                color: '#535353'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#404040',
                                borderColor: '#535353',
                                lineWidth: 1
                            },
                            ticks: {
                                color: '#b3b3b3',
                                font: {
                                    family: 'Circular, Helvetica Neue, Arial, sans-serif',
                                    size: 12
                                },
                                padding: 10,
                                callback: function(value) {
                                    return new Intl.NumberFormat('fr-FR').format(value) + ' <?php echo APP_CURRENCY_SYMBOL; ?>';
                                }
                            },
                            border: {
                                color: '#535353'
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
