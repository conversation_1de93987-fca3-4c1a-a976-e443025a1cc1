<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

$pageTitle = 'Reset Password';
$message = '';
$messageType = '';
$token = $_GET['token'] ?? '';
$validToken = false;

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../user/dashboard.php');
    exit;
}

// Validate token
if (!empty($token)) {
    $resetData = $userManager->validateResetToken($token);
    if ($resetData) {
        $validToken = true;
    } else {
        $message = 'Invalid or expired reset token. Please request a new password reset.';
        $messageType = 'danger';
    }
} else {
    $message = 'No reset token provided. Please request a new password reset.';
    $messageType = 'danger';
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && $validToken) {
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $password = $_POST['password'];
        $confirmPassword = $_POST['confirm_password'];
        
        if (empty($password) || empty($confirmPassword)) {
            $message = 'Please fill in all fields.';
            $messageType = 'danger';
        } elseif (strlen($password) < 6) {
            $message = 'Password must be at least 6 characters long.';
            $messageType = 'danger';
        } elseif ($password !== $confirmPassword) {
            $message = 'Passwords do not match.';
            $messageType = 'danger';
        } else {
            if ($userManager->resetPassword($token, $password)) {
                $message = 'Your password has been reset successfully! You can now sign in with your new password.';
                $messageType = 'success';
                $validToken = false; // Prevent further submissions
            } else {
                $message = 'Failed to reset password. Please try again or request a new reset link.';
                $messageType = 'danger';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                ZARA-Events
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../">Home</a>
                <a class="nav-link" href="login.php">Sign In</a>
                <a class="nav-link" href="register.php">Sign Up</a>
            </div>
        </div>
    </nav>

    <!-- Reset Password Section -->
    <section class="hero-modern d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <div class="form-modern animate-on-scroll">
                        <div class="text-center mb-4">
                            <div class="mb-3">
                                <i class="fas fa-lock-open fa-3x" style="color: var(--primary-color);"></i>
                            </div>
                            <h2 class="fw-bold mb-2">Reset Your Password</h2>
                            <p class="text-muted">Enter your new password below</p>
                        </div>

                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($validToken): ?>
                            <form method="POST" id="resetPasswordForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="form-floating-modern">
                                    <input type="password" class="form-control-modern" 
                                           id="password" name="password" required 
                                           placeholder=" " minlength="6">
                                    <label for="password">New Password</label>
                                </div>

                                <div class="form-floating-modern">
                                    <input type="password" class="form-control-modern" 
                                           id="confirm_password" name="confirm_password" required 
                                           placeholder=" " minlength="6">
                                    <label for="confirm_password">Confirm New Password</label>
                                </div>

                                <!-- Password Strength Indicator -->
                                <div class="password-strength mb-3" id="passwordStrength" style="display: none;">
                                    <div class="strength-bar">
                                        <div class="strength-fill"></div>
                                    </div>
                                    <small class="strength-text"></small>
                                </div>

                                <button type="submit" class="btn btn-primary-enhanced w-100 mb-3">
                                    <i class="fas fa-check me-2"></i>
                                    Reset Password
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="text-center">
                                <a href="forgot-password.php" class="btn btn-primary-enhanced">
                                    <i class="fas fa-redo me-2"></i>
                                    Request New Reset Link
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="text-center mt-3">
                            <p class="mb-0">
                                <a href="login.php" class="text-decoration-none">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Back to Sign In
                                </a>
                            </p>
                        </div>

                        <!-- Password Requirements -->
                        <?php if ($validToken): ?>
                            <div class="mt-4 p-3" style="background: rgba(59, 130, 246, 0.1); border-radius: 12px; border-left: 4px solid #3b82f6;">
                                <h6 class="fw-bold mb-2" style="color: #1e40af;">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Password Requirements
                                </h6>
                                <ul class="mb-0" style="font-size: 0.875rem; color: #6b7280;">
                                    <li>At least 6 characters long</li>
                                    <li>Mix of letters and numbers recommended</li>
                                    <li>Avoid common passwords</li>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>
    
    <script>
        // Form validation and submission
        document.getElementById('resetPasswordForm')?.addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (!password || !confirmPassword) {
                e.preventDefault();
                showToast('Please fill in all fields.', 'error');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                showToast('Password must be at least 6 characters long.', 'error');
                return false;
            }
            
            if (password !== confirmPassword) {
                e.preventDefault();
                showToast('Passwords do not match.', 'error');
                return false;
            }
        });

        // Password strength checker
        document.getElementById('password')?.addEventListener('input', function() {
            const password = this.value;
            const strengthIndicator = document.getElementById('passwordStrength');
            const strengthFill = strengthIndicator.querySelector('.strength-fill');
            const strengthText = strengthIndicator.querySelector('.strength-text');
            
            if (password.length === 0) {
                strengthIndicator.style.display = 'none';
                return;
            }
            
            strengthIndicator.style.display = 'block';
            
            let strength = 0;
            let text = '';
            let color = '';
            
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    text = 'Very Weak';
                    color = '#ef4444';
                    break;
                case 2:
                    text = 'Weak';
                    color = '#f59e0b';
                    break;
                case 3:
                    text = 'Fair';
                    color = '#eab308';
                    break;
                case 4:
                    text = 'Good';
                    color = '#22c55e';
                    break;
                case 5:
                    text = 'Strong';
                    color = '#10b981';
                    break;
            }
            
            strengthFill.style.width = (strength * 20) + '%';
            strengthFill.style.backgroundColor = color;
            strengthText.textContent = text;
            strengthText.style.color = color;
        });

        // Enhanced floating label behavior
        document.querySelectorAll('.form-control-modern').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    </script>

    <style>
        .password-strength {
            margin-top: 0.5rem;
        }
        
        .strength-bar {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 0.25rem;
        }
        
        .strength-fill {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-text {
            font-size: 0.75rem;
            font-weight: 500;
        }
    </style>
</body>
</html>
