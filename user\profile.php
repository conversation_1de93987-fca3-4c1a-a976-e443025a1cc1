<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php');
}

$pageTitle = 'My Profile';
$user = $userManager->getUserById($_SESSION['user_id']);
$userId = $_SESSION['user_id'];

// Get user statistics
$db->query('SELECT
    COUNT(*) as total_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN 1 ELSE 0 END) as confirmed_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN quantity ELSE 0 END) as total_tickets,
    SUM(CASE WHEN booking_status = "confirmed" THEN total_amount ELSE 0 END) as total_spent
    FROM bookings
    WHERE user_id = :user_id');
$db->bind(':user_id', $userId);
$stats = $db->single();

// Get events attended (past confirmed events)
$db->query('SELECT COUNT(*) as events_attended
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           WHERE b.user_id = :user_id
           AND b.booking_status = "confirmed"
           AND e.event_date < CURDATE()');
$db->bind(':user_id', $userId);
$attendedResult = $db->single();
$eventsAttended = $attendedResult ? $attendedResult->events_attended : 0;

// Get upcoming events count
$db->query('SELECT COUNT(*) as upcoming_events
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           WHERE b.user_id = :user_id
           AND b.booking_status = "confirmed"
           AND e.event_date >= CURDATE()');
$db->bind(':user_id', $userId);
$upcomingResult = $db->single();
$upcomingEvents = $upcomingResult ? $upcomingResult->upcoming_events : 0;

// Get cart count
$cartCount = $cartManager->getCartCount($userId);

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $updateData = [
        'first_name' => trim($_POST['first_name']),
        'last_name' => trim($_POST['last_name']),
        'phone' => trim($_POST['phone']),
        'address' => trim($_POST['address'])
    ];

    // Validate input
    $errors = [];
    if (empty($updateData['first_name'])) {
        $errors[] = 'First name is required';
    }
    if (empty($updateData['last_name'])) {
        $errors[] = 'Last name is required';
    }

    if (empty($errors)) {
        if ($userManager->updateProfile($_SESSION['user_id'], $updateData)) {
            setFlashMessage('success', 'Profile updated successfully!');
            // Update session data
            $_SESSION['first_name'] = $updateData['first_name'];
            $_SESSION['last_name'] = $updateData['last_name'];
            redirect('profile.php');
        } else {
            setFlashMessage('error', 'Failed to update profile. Please try again.');
        }
    } else {
        setFlashMessage('error', implode('<br>', $errors));
    }
}

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Ukuqala Events</title>

    <!-- African-Inspired Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Playfair+Display:wght@400;500;600;700;800&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--hero-gradient);
            background-attachment: fixed;
            min-height: 100vh;
        }

        .main-content {
            padding-top: 120px;
            padding-bottom: 60px;
        }

        .profile-hero {
            background: var(--earth-gradient);
            color: white;
            padding: 4rem 0 3rem;
            margin-bottom: 3rem;
            border-radius: var(--border-radius-lg);
            position: relative;
            overflow: hidden;
        }

        .profile-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--pattern-tribal);
            opacity: 0.1;
        }

        .profile-avatar {
            width: 150px;
            height: 150px;
            background: var(--sunset-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            border: 5px solid white;
            box-shadow: var(--shadow-strong);
            position: relative;
            z-index: 2;
        }

        .profile-card {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-cultural);
            overflow: hidden;
            position: relative;
        }

        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--sunset-gradient);
        }

        .quick-action-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 2px solid var(--primary-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            transition: var(--transition-cultural);
            height: 100%;
        }

        .quick-action-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-cultural);
            border-color: var(--accent-color);
        }

        .quick-action-icon {
            width: 70px;
            height: 70px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.8rem;
            box-shadow: var(--shadow-soft);
        }

        .form-section {
            background: rgba(245, 245, 220, 0.9);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 2.5rem;
            margin-bottom: 2rem;
            border: 2px solid var(--glass-border);
        }

        .section-title {
            color: var(--text-primary);
            font-family: var(--font-display);
            font-weight: 800;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
        }

        .section-title i {
            color: var(--accent-color);
        }

        .profile-stats {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: 2.5rem;
            text-align: center;
            box-shadow: var(--shadow-cultural);
            position: relative;
            overflow: hidden;
        }

        .profile-stats::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--pattern-geometric);
            opacity: 0.1;
        }

        .stat-item {
            margin-bottom: 2rem;
            position: relative;
            z-index: 2;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            font-family: var(--font-display);
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            font-size: 1rem;
            font-weight: 600;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .member-badge {
            background: var(--sunset-gradient);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            font-weight: 700;
            margin: 0.5rem;
            display: inline-block;
            box-shadow: var(--shadow-soft);
        }

        .profile-info-card {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-cultural);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-drum me-2"></i>
                Ukuqala Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle"
                                  <?php echo $cartCount > 0 ? '' : 'style="display: none;"'; ?>>
                                <?php echo $cartCount; ?>
                            </span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($user->first_name); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a></li>
                            <li><a class="dropdown-item active" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="bookings.php">
                                <i class="fas fa-ticket-alt me-2"></i>My Bookings
                            </a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">
                                    <i class="fas fa-cog me-2"></i>Admin Panel
                                </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Profile Hero Section -->
            <div class="profile-hero">
                <div class="text-center">
                    <div class="profile-avatar">
                        <i class="fas fa-user fa-4x text-white"></i>
                    </div>
                    <h1 class="display-3 fw-bold mb-3">
                        <?php echo htmlspecialchars($user->first_name . ' ' . $user->last_name); ?>
                    </h1>
                    <p class="lead mb-4">
                        <i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($user->email); ?>
                    </p>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <span class="member-badge">
                            <i class="fas fa-crown me-2"></i><?php echo ucfirst($user->role); ?>
                        </span>
                        <span class="member-badge">
                            <i class="fas fa-calendar me-2"></i>Member since <?php echo date('M Y', strtotime($user->created_at)); ?>
                        </span>
                        <?php if ($stats->total_bookings > 0): ?>
                            <span class="member-badge">
                                <i class="fas fa-star me-2"></i>Active Member
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <!-- Quick Actions -->
            <div class="row mb-5">
                <div class="col-12">
                    <h3 class="section-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-2 text-cultural">Dashboard</h5>
                        <p class="text-muted mb-3">View your bookings and activity overview</p>
                        <a href="dashboard.php" class="btn btn-primary-modern">Go to Dashboard</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-2 text-cultural">My Bookings</h5>
                        <p class="text-muted mb-3">View your event booking history</p>
                        <a href="bookings.php" class="btn btn-accent-modern">View Bookings</a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h5 class="fw-bold mb-2 text-cultural">My Cart</h5>
                        <p class="text-muted mb-3">Review items in your cart</p>
                        <a href="../booking/cart.php" class="btn btn-secondary-modern">
                            View Cart <?php if ($cartCount > 0): ?><span class="badge bg-light text-dark ms-1"><?php echo $cartCount; ?></span><?php endif; ?>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-calendar-star"></i>
                        </div>
                        <h5 class="fw-bold mb-2 text-cultural">Browse Events</h5>
                        <p class="text-muted mb-3">Discover amazing cultural events</p>
                        <a href="../events/" class="btn btn-outline-modern">Browse Events</a>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Profile Stats -->
                <div class="col-lg-4 mb-4">
                    <div class="profile-stats">
                        <h4 class="fw-bold mb-4">
                            <i class="fas fa-chart-line me-2"></i>
                            Your Journey Stats
                        </h4>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($stats->total_bookings ?? 0); ?></div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($eventsAttended); ?></div>
                            <div class="stat-label">Events Attended</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($upcomingEvents); ?></div>
                            <div class="stat-label">Upcoming Events</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo number_format($stats->total_tickets ?? 0); ?></div>
                            <div class="stat-label">Total Tickets</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo formatCurrency($stats->total_spent ?? 0); ?></div>
                            <div class="stat-label">Total Spent</div>
                        </div>
                    </div>

                    <!-- Additional Info Card -->
                    <div class="profile-info-card mt-4">
                        <h5 class="fw-bold mb-3 text-cultural">
                            <i class="fas fa-info-circle me-2"></i>
                            Account Information
                        </h5>
                        <div class="mb-2">
                            <strong>Account Status:</strong>
                            <span class="badge bg-success ms-2">Active</span>
                        </div>
                        <div class="mb-2">
                            <strong>Member Level:</strong>
                            <span class="text-primary">
                                <?php
                                $totalBookings = $stats->total_bookings ?? 0;
                                if ($totalBookings >= 10) echo "Gold Member";
                                elseif ($totalBookings >= 5) echo "Silver Member";
                                elseif ($totalBookings >= 1) echo "Bronze Member";
                                else echo "New Member";
                                ?>
                            </span>
                        </div>
                        <div class="mb-2">
                            <strong>Joined:</strong> <?php echo formatDate($user->created_at); ?>
                        </div>
                        <div class="mb-2">
                            <strong>Last Updated:</strong> <?php echo formatDate($user->updated_at ?? $user->created_at); ?>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="col-lg-8">
                    <?php if ($flashMessage): ?>
                        <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                            <?php echo $flashMessage['message']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="profile-card">
                        <div class="card-body p-0">
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-user-edit"></i>
                                    Personal Information
                                </h4>
                                <form method="POST" action="">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="first_name" class="form-label">First Name *</label>
                                            <input type="text" class="form-control form-control-modern" id="first_name"
                                                   name="first_name" value="<?php echo htmlspecialchars($user->first_name); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_name" class="form-label">Last Name *</label>
                                            <input type="text" class="form-control form-control-modern" id="last_name"
                                                   name="last_name" value="<?php echo htmlspecialchars($user->last_name); ?>" required>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control form-control-modern" id="email"
                                                   value="<?php echo htmlspecialchars($user->email); ?>" readonly>
                                            <div class="form-text text-muted">
                                                <i class="fas fa-lock me-1"></i>Email cannot be changed. Contact support if needed.
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="username" class="form-label">Username</label>
                                            <input type="text" class="form-control form-control-modern" id="username"
                                                   value="<?php echo htmlspecialchars($user->username); ?>" readonly>
                                            <div class="form-text text-muted">
                                                <i class="fas fa-lock me-1"></i>Username cannot be changed.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control form-control-modern" id="phone"
                                                   name="phone" value="<?php echo htmlspecialchars($user->phone); ?>"
                                                   placeholder="+27 ************">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="member_since" class="form-label">Member Since</label>
                                            <input type="text" class="form-control form-control-modern" id="member_since"
                                                   value="<?php echo formatDate($user->created_at); ?>" readonly>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label for="address" class="form-label">Address</label>
                                        <textarea class="form-control form-control-modern" id="address" name="address"
                                                  rows="3" placeholder="Enter your full address..."><?php echo htmlspecialchars($user->address); ?></textarea>
                                    </div>

                                    <div class="d-flex justify-content-between flex-wrap gap-3">
                                        <a href="dashboard.php" class="btn btn-outline-modern">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                        </a>
                                        <div class="d-flex gap-2">
                                            <a href="bookings.php" class="btn btn-secondary-modern">
                                                <i class="fas fa-ticket-alt me-2"></i>View Bookings
                                            </a>
                                            <button type="submit" class="btn btn-primary-modern">
                                                <i class="fas fa-save me-2"></i>Update Profile
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load cart count
            loadCartCount();

            // Navbar scroll effect
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar-modern');
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Add hover effects to quick action cards
            const quickActionCards = document.querySelectorAll('.quick-action-card');
            quickActionCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Form validation enhancement
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const firstName = document.getElementById('first_name').value.trim();
                    const lastName = document.getElementById('last_name').value.trim();

                    if (!firstName || !lastName) {
                        e.preventDefault();
                        alert('Please fill in both first name and last name.');
                        return false;
                    }

                    // Show loading state
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
                    submitBtn.disabled = true;

                    // Re-enable after 3 seconds in case of error
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 3000);
                });
            }

            // Animate statistics on scroll
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -100px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const statNumbers = entry.target.querySelectorAll('.stat-number');
                        statNumbers.forEach(stat => {
                            const finalValue = stat.textContent;
                            stat.textContent = '0';

                            // Simple counter animation
                            let current = 0;
                            const increment = 1;
                            const timer = setInterval(() => {
                                current += increment;
                                stat.textContent = current;

                                if (current >= parseInt(finalValue) || current >= 100) {
                                    clearInterval(timer);
                                    stat.textContent = finalValue;
                                }
                            }, 50);
                        });

                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            const statsSection = document.querySelector('.profile-stats');
            if (statsSection) {
                observer.observe(statsSection);
            }
        });

        // Load cart count function
        async function loadCartCount() {
            try {
                const response = await fetch('../booking/get_cart_count.php');
                const result = await response.json();

                if (result.success) {
                    const cartCountElement = document.querySelector('.cart-count');
                    if (result.cart_count > 0) {
                        cartCountElement.textContent = result.cart_count;
                        cartCountElement.style.display = 'block';
                    } else {
                        cartCountElement.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Failed to load cart count:', error);
            }
        }
    </script>
</body>
</html>
