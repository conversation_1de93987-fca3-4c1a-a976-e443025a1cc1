<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

$pageTitle = 'Dashboard';
$userId = $_SESSION['user_id'];

// Get user's bookings
$userBookings = $bookingManager->getUserBookings($userId);

// Get user's cart count
$cartCount = $cartManager->getCartCount($userId);

// Get user info
$user = $userManager->getUserById($userId);

// Get comprehensive user statistics
$db->query('SELECT
    COUNT(*) as total_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN 1 ELSE 0 END) as confirmed_bookings,
    SUM(CASE WHEN booking_status = "pending" THEN 1 ELSE 0 END) as pending_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN quantity ELSE 0 END) as total_tickets,
    SUM(CASE WHEN booking_status = "confirmed" THEN total_amount ELSE 0 END) as total_spent
    FROM bookings
    WHERE user_id = :user_id');
$db->bind(':user_id', $userId);
$stats = $db->single();

// Get upcoming events
$db->query('SELECT COUNT(*) as upcoming_events
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           WHERE b.user_id = :user_id
           AND b.booking_status = "confirmed"
           AND e.event_date >= CURDATE()');
$db->bind(':user_id', $userId);
$upcomingResult = $db->single();
$upcomingEvents = $upcomingResult ? $upcomingResult->upcoming_events : 0;

// Get events attended (past confirmed events)
$db->query('SELECT COUNT(*) as events_attended
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           WHERE b.user_id = :user_id
           AND b.booking_status = "confirmed"
           AND e.event_date < CURDATE()');
$db->bind(':user_id', $userId);
$attendedResult = $db->single();
$eventsAttended = $attendedResult ? $attendedResult->events_attended : 0;

// Get recent events for quick access
$db->query('SELECT b.*, e.title, e.description, e.event_date, e.event_time, e.venue, e.category, e.image_url
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           WHERE b.user_id = :user_id
           ORDER BY b.created_at DESC
           LIMIT 5');
$db->bind(':user_id', $userId);
$recentBookings = $db->resultset();

// Get next upcoming event
$db->query('SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.image_url
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           WHERE b.user_id = :user_id
           AND b.booking_status = "confirmed"
           AND e.event_date >= CURDATE()
           ORDER BY e.event_date ASC
           LIMIT 1');
$db->bind(':user_id', $userId);
$nextEvent = $db->single();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Ukuqala Events</title>

    <!-- African-Inspired Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Playfair+Display:wght@400;500;600;700;800&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--hero-gradient);
            background-attachment: fixed;
            min-height: 100vh;
        }

        .main-content {
            padding-top: 120px;
            padding-bottom: 60px;
        }

        .dashboard-hero {
            background: var(--earth-gradient);
            color: white;
            padding: 4rem 0 3rem;
            margin-bottom: 3rem;
            border-radius: var(--border-radius-lg);
            position: relative;
            overflow: hidden;
        }

        .dashboard-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--pattern-tribal);
            opacity: 0.1;
        }

        .welcome-card {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-cultural);
            position: relative;
            overflow: hidden;
        }

        .welcome-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--sunset-gradient);
        }

        .next-event-card {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-cultural);
            position: relative;
            overflow: hidden;
        }

        .next-event-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--pattern-geometric);
            opacity: 0.1;
        }

        .booking-card {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius);
            margin-bottom: 1.5rem;
            transition: var(--transition-cultural);
            overflow: hidden;
            position: relative;
        }

        .booking-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--sunset-gradient);
        }

        .booking-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-cultural);
            border-color: var(--accent-color);
        }

        .booking-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: var(--border-radius);
        }

        .quick-action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .quick-action-item {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 2px solid var(--primary-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            transition: var(--transition-cultural);
            text-decoration: none;
            color: inherit;
        }

        .quick-action-item:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-cultural);
            border-color: var(--accent-color);
            color: inherit;
        }

        .quick-action-icon {
            width: 70px;
            height: 70px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.8rem;
            box-shadow: var(--shadow-soft);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 700;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-confirmed {
            background: var(--forest-gradient);
            color: white;
        }

        .status-pending {
            background: var(--sunset-gradient);
            color: white;
        }

        .status-cancelled {
            background: #dc3545;
            color: white;
        }

        .empty-state {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 4rem 2rem;
            text-align: center;
            box-shadow: var(--shadow-cultural);
        }

        .empty-state-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-drum me-2"></i>
                Ukuqala Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">Dashboard</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle"
                                  <?php echo $cartCount > 0 ? '' : 'style="display: none;"'; ?>>
                                <?php echo $cartCount; ?>
                            </span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($user->first_name); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="bookings.php">
                                <i class="fas fa-ticket-alt me-2"></i>My Bookings
                            </a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">
                                    <i class="fas fa-cog me-2"></i>Admin Panel
                                </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Dashboard Hero -->
            <div class="dashboard-hero">
                <div class="text-center">
                    <h1 class="display-3 fw-bold mb-3">
                        Sawubona, <?php echo htmlspecialchars($user->first_name); ?>! 👋
                    </h1>
                    <p class="lead mb-4">
                        Welcome to your personal event journey dashboard
                    </p>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <span class="badge bg-light text-dark px-3 py-2 fs-6">
                            <i class="fas fa-crown me-2"></i><?php echo ucfirst($user->role); ?>
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2 fs-6">
                            <i class="fas fa-calendar me-2"></i>Member since <?php echo date('M Y', strtotime($user->created_at)); ?>
                        </span>
                    </div>
                </div>
            </div>
            <!-- Statistics Cards -->
            <div class="row mb-5">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stat-icon bg-primary text-white mx-auto">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats->total_bookings ?? 0); ?></div>
                        <div class="stat-label">Total Bookings</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stat-icon bg-success text-white mx-auto">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($upcomingEvents); ?></div>
                        <div class="stat-label">Upcoming Events</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stat-icon bg-warning text-white mx-auto">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats->total_tickets ?? 0); ?></div>
                        <div class="stat-label">Total Tickets</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stat-icon bg-info text-white mx-auto">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($cartCount); ?></div>
                        <div class="stat-label">Items in Cart</div>
                    </div>
                </div>
            </div>

            <div class="row mb-5">
                <!-- Welcome Card -->
                <div class="col-lg-8 mb-4">
                    <div class="welcome-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h3 class="fw-bold mb-3 text-cultural">
                                    <i class="fas fa-heart me-2"></i>
                                    Ubuntu Spirit Dashboard
                                </h3>
                                <p class="text-muted mb-3">
                                    "I am because we are" - Your journey with Ukuqala Events connects you to a community
                                    of culture enthusiasts. Together we celebrate, learn, and grow.
                                </p>
                                <div class="d-flex gap-2 flex-wrap">
                                    <span class="badge bg-primary">
                                        <?php echo number_format($eventsAttended); ?> Events Attended
                                    </span>
                                    <span class="badge bg-success">
                                        <?php echo formatCurrency($stats->total_spent ?? 0); ?> Total Invested
                                    </span>
                                    <span class="badge bg-info">
                                        Active Member
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-users fa-4x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Next Event Card -->
                <div class="col-lg-4 mb-4">
                    <?php if ($nextEvent): ?>
                        <div class="next-event-card">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-calendar-star me-2"></i>
                                Next Event
                            </h5>
                            <h6 class="mb-2"><?php echo htmlspecialchars($nextEvent->title); ?></h6>
                            <div class="mb-2">
                                <i class="fas fa-calendar me-2"></i>
                                <?php echo formatDate($nextEvent->event_date); ?>
                            </div>
                            <div class="mb-3">
                                <i class="fas fa-clock me-2"></i>
                                <?php echo formatTime($nextEvent->event_time); ?>
                            </div>
                            <a href="../events/details.php?id=<?php echo $nextEvent->event_id; ?>"
                               class="btn btn-light">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="next-event-card">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-calendar-plus me-2"></i>
                                Book Your Next Adventure
                            </h5>
                            <p class="mb-3">No upcoming events yet. Discover amazing cultural experiences waiting for you!</p>
                            <a href="../events/" class="btn btn-light">
                                <i class="fas fa-search me-2"></i>Browse Events
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-5">
                <div class="col-12">
                    <h3 class="section-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h3>
                    <div class="quick-action-grid">
                        <a href="../events/" class="quick-action-item">
                            <div class="quick-action-icon">
                                <i class="fas fa-calendar-star"></i>
                            </div>
                            <h5 class="fw-bold mb-2 text-cultural">Browse Events</h5>
                            <p class="text-muted mb-0">Discover amazing cultural experiences</p>
                        </a>

                        <a href="bookings.php" class="quick-action-item">
                            <div class="quick-action-icon">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <h5 class="fw-bold mb-2 text-cultural">My Bookings</h5>
                            <p class="text-muted mb-0">View your event history</p>
                        </a>

                        <a href="../booking/cart.php" class="quick-action-item">
                            <div class="quick-action-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h5 class="fw-bold mb-2 text-cultural">Shopping Cart</h5>
                            <p class="text-muted mb-0">
                                Review your selections
                                <?php if ($cartCount > 0): ?>
                                    <span class="badge bg-danger ms-1"><?php echo $cartCount; ?></span>
                                <?php endif; ?>
                            </p>
                        </a>

                        <a href="profile.php" class="quick-action-item">
                            <div class="quick-action-icon">
                                <i class="fas fa-user-edit"></i>
                            </div>
                            <h5 class="fw-bold mb-2 text-cultural">Edit Profile</h5>
                            <p class="text-muted mb-0">Update your information</p>
                        </a>

                        <a href="../events/search.php" class="quick-action-item">
                            <div class="quick-action-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h5 class="fw-bold mb-2 text-cultural">Search Events</h5>
                            <p class="text-muted mb-0">Find specific events</p>
                        </a>

                        <?php if (isAdmin()): ?>
                            <a href="../admin/" class="quick-action-item">
                                <div class="quick-action-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <h5 class="fw-bold mb-2 text-cultural">Admin Panel</h5>
                                <p class="text-muted mb-0">Manage the platform</p>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3 class="section-title">
                            <i class="fas fa-history"></i>
                            Recent Bookings
                        </h3>
                        <a href="bookings.php" class="btn btn-primary-modern">
                            <i class="fas fa-list me-2"></i>View All Bookings
                        </a>
                    </div>

                    <?php if (!empty($recentBookings)): ?>
                        <?php foreach ($recentBookings as $booking): ?>
                            <div class="booking-card">
                                <div class="row align-items-center p-3">
                                    <div class="col-md-2">
                                        <img src="<?php echo $booking->image_url ?: '../images/africa1.png'; ?>"
                                             alt="<?php echo htmlspecialchars($booking->title); ?>"
                                             class="booking-image">
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="fw-bold mb-1 text-cultural">
                                            <?php echo htmlspecialchars($booking->title); ?>
                                        </h6>
                                        <div class="text-muted small mb-1">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo formatDate($booking->event_date); ?>
                                        </div>
                                        <div class="text-muted small mb-1">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo formatTime($booking->event_time); ?>
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <?php echo htmlspecialchars($booking->venue); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <div class="fw-bold">
                                            <?php echo $booking->quantity; ?> ticket<?php echo $booking->quantity > 1 ? 's' : ''; ?>
                                        </div>
                                        <div class="text-primary fw-bold">
                                            <?php echo formatCurrency($booking->total_amount); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <div class="status-badge status-<?php echo $booking->booking_status; ?> mb-2">
                                            <?php echo ucfirst($booking->booking_status); ?>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo formatDate($booking->created_at); ?>
                                        </small>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <div class="d-flex gap-1 justify-content-center">
                                            <a href="../events/details.php?id=<?php echo $booking->event_id; ?>"
                                               class="btn btn-outline-modern btn-sm" title="View Event">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($booking->booking_status === 'confirmed'): ?>
                                                <button class="btn btn-accent-modern btn-sm"
                                                        onclick="downloadTicket(<?php echo $booking->id; ?>)"
                                                        title="Download Ticket">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-calendar-times"></i>
                            </div>
                            <h4 class="fw-bold mb-3 text-cultural">No Bookings Yet</h4>
                            <p class="lead text-muted mb-4">
                                Your event journey is waiting to begin!<br>
                                Discover amazing cultural experiences and start creating memories.
                            </p>
                            <div class="d-grid gap-3 d-md-flex justify-content-md-center">
                                <a href="../events/" class="btn btn-primary-modern btn-lg">
                                    <i class="fas fa-calendar-star me-2"></i>Browse Events
                                </a>
                                <a href="../events/search.php" class="btn btn-accent-modern btn-lg">
                                    <i class="fas fa-search me-2"></i>Search Events
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load cart count
            loadCartCount();

            // Navbar scroll effect
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar-modern');
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Add hover effects to booking cards
            const bookingCards = document.querySelectorAll('.booking-card');
            bookingCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add hover effects to quick action items
            const quickActionItems = document.querySelectorAll('.quick-action-item');
            quickActionItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Animate statistics on scroll
            const observerOptions = {
                threshold: 0.5,
                rootMargin: '0px 0px -100px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const statNumbers = entry.target.querySelectorAll('.stat-number');
                        statNumbers.forEach(stat => {
                            const finalValue = stat.textContent;
                            const numericValue = parseInt(finalValue.replace(/[^0-9]/g, ''));

                            if (numericValue > 0) {
                                stat.textContent = '0';

                                // Simple counter animation
                                let current = 0;
                                const increment = Math.ceil(numericValue / 30);
                                const timer = setInterval(() => {
                                    current += increment;
                                    if (current >= numericValue) {
                                        clearInterval(timer);
                                        stat.textContent = finalValue;
                                    } else {
                                        stat.textContent = current.toLocaleString();
                                    }
                                }, 50);
                            }
                        });

                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            const statsCards = document.querySelectorAll('.stats-card');
            statsCards.forEach(card => {
                observer.observe(card);
            });
        });

        // Download ticket function
        function downloadTicket(bookingId) {
            const ticketData = {
                bookingId: bookingId,
                timestamp: new Date().toISOString(),
                eventName: 'Ukuqala Events Ticket',
                message: 'Thank you for booking with Ukuqala Events!'
            };

            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(ticketData, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", `ukuqala-ticket-${bookingId}.json`);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();

            alert('Ticket downloaded successfully! Please keep this for your records.');
        }

        // Load cart count function
        async function loadCartCount() {
            try {
                const response = await fetch('../booking/get_cart_count.php');
                const result = await response.json();

                if (result.success) {
                    const cartCountElement = document.querySelector('.cart-count');
                    if (result.cart_count > 0) {
                        cartCountElement.textContent = result.cart_count;
                        cartCountElement.style.display = 'block';
                    } else {
                        cartCountElement.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Failed to load cart count:', error);
            }
        }
    </script>
</body>
</html>
