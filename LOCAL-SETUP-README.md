# Ukuqala Events - Local Development Setup

This guide will help you run the Ukuqala Events application locally using Docker for the database and web server.

## Prerequisites

- **Docker Desktop** installed and running
- **Git** (if cloning the repository)

## Quick Start

### Option 1: Automated Setup (Recommended)

1. **Start the Application**
   ```bash
   # On Windows
   start-local.bat
   
   # On Linux/Mac
   ./quick-start.sh
   ```

2. **Access the Application**
   - **Main Application**: http://localhost:8000
   - **Admin Panel**: http://localhost:8000/admin/
   - **phpMyAdmin**: http://localhost:8081

### Option 2: Manual Setup

1. **Start Services**
   ```bash
   docker-compose up -d --build
   ```

2. **Check Status**
   ```bash
   docker-compose ps
   ```

3. **Stop Services**
   ```bash
   docker-compose down
   ```

## Services Overview

| Service | Container Name | Port | Purpose |
|---------|---------------|------|---------|
| Web App | event_booking_web | 8000 | PHP Application |
| MySQL | event_booking_mysql | 3307 | Database |
| phpMyAdmin | event_booking_phpmyadmin | 8081 | Database Management |

## Database Configuration

- **Host**: localhost:3307 (external) / mysql:3306 (internal)
- **Database**: event_booking_system
- **Username**: event_user
- **Password**: event_password
- **Root Password**: root_password

## Default Admin Account

- **Email**: <EMAIL>
- **Password**: password

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   netstat -ano | findstr :8000
   
   # Stop existing containers
   docker-compose down
   ```

2. **MySQL Connection Issues**
   ```bash
   # Check MySQL logs
   docker-compose logs mysql
   
   # Restart MySQL container
   docker-compose restart mysql
   ```

3. **Application Not Loading**
   ```bash
   # Check web container logs
   docker-compose logs web
   
   # Rebuild containers
   docker-compose up -d --build
   ```

### Useful Commands

```bash
# View all container logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f web
docker-compose logs -f mysql

# Access MySQL directly
docker exec -it event_booking_mysql mysql -u event_user -p

# Access web container shell
docker exec -it event_booking_web bash

# Restart specific service
docker-compose restart web
```

## Development Workflow

1. **Make Code Changes**: Edit files in your local directory
2. **Restart Web Container**: `docker-compose restart web`
3. **View Changes**: Refresh http://localhost:8000

## File Structure

```
├── assets/           # CSS, JS, Images
├── auth/            # Authentication pages
├── admin/           # Admin panel
├── booking/         # Booking functionality
├── database/        # SQL schema and data
├── includes/        # PHP configuration and functions
├── user/            # User dashboard
├── docker-compose.yml
├── Dockerfile
└── start-local.bat  # Windows startup script
```

## Next Steps

1. Visit http://localhost:8000 to see the application
2. Create a user account or login as admin
3. Explore the event booking functionality
4. Access phpMyAdmin at http://localhost:8081 to manage the database

## Support

If you encounter any issues:
1. Check the troubleshooting section above
2. Review container logs: `docker-compose logs`
3. Ensure Docker Desktop is running
4. Try rebuilding: `docker-compose up -d --build`
