<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in, if not redirect to welcome page
if (!isLoggedIn()) {
    redirect('../welcome.php');
}

$pageTitle = 'Browse Events';

// Get filter parameters
$category = $_GET['category'] ?? '';
$location = $_GET['location'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = EVENTS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get events based on filters
if (!empty($search)) {
    $events = $eventManager->searchEvents($search, $location);
} elseif (!empty($category)) {
    $events = $eventManager->getEventsByCategory($category);
} else {
    $events = $eventManager->getAllEvents($limit, $offset);
}

// Get total count for pagination
$totalEvents = $eventManager->getTotalEventsCount();
$totalPages = ceil($totalEvents / $limit);

// Get unique categories and locations for filters
$db->query('SELECT DISTINCT category FROM events WHERE status = "active" ORDER BY category');
$categories = $db->resultset();

$db->query('SELECT DISTINCT location FROM events WHERE status = "active" ORDER BY location');
$locations = $db->resultset();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--secondary-light);
        }

        .events-hero {
            background: var(--primary-gradient);
            color: white;
            padding: var(--space-3xl) 0 var(--space-2xl);
            margin-top: 80px;
            border-radius: 0 0 30px 30px;
            position: relative;
            overflow: hidden;
        }

        .events-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .events-hero .container {
            position: relative;
            z-index: 2;
        }

        .filter-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-medium);
            margin-top: -50px;
            position: relative;
            z-index: 3;
        }

        .event-card-enhanced {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-light);
            transition: var(--transition-spring);
            overflow: hidden;
            position: relative;
            margin-bottom: var(--space-xl);
        }

        .event-card-enhanced:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
        }

        .event-card-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .event-image-container {
            position: relative;
            overflow: hidden;
            height: 200px;
        }

        .event-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition-smooth);
        }

        .event-card-enhanced:hover .event-image {
            transform: scale(1.05);
        }

        .event-badge {
            position: absolute;
            top: var(--space-md);
            right: var(--space-md);
            background: var(--primary-color);
            color: white;
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--border-radius);
            font-size: var(--text-xs);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .event-price-tag {
            position: absolute;
            bottom: var(--space-md);
            left: var(--space-md);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--border-radius);
            font-weight: 600;
        }

        .event-content {
            padding: var(--space-xl);
        }

        .event-title {
            font-size: var(--text-xl);
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: var(--space-sm);
            line-height: 1.3;
        }

        .event-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
            font-size: var(--text-sm);
            color: var(--text-muted);
        }

        .event-meta-item {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .event-description {
            color: var(--text-muted);
            line-height: 1.6;
            margin-bottom: var(--space-lg);
        }

        .event-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--space-md);
        }

        .tickets-remaining {
            font-size: var(--text-sm);
            color: var(--primary-color);
            font-weight: 600;
        }

        .tickets-remaining.low {
            color: #f59e0b;
        }

        .tickets-remaining.sold-out {
            color: #ef4444;
        }

        .pagination-modern {
            display: flex;
            justify-content: center;
            gap: var(--space-sm);
            margin-top: var(--space-2xl);
        }

        .pagination-modern .page-link {
            border: 2px solid #e5e7eb;
            border-radius: var(--border-radius);
            padding: var(--space-sm) var(--space-md);
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition-smooth);
            font-weight: 500;
        }

        .pagination-modern .page-link:hover,
        .pagination-modern .page-link.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .no-events-state {
            text-align: center;
            padding: var(--space-3xl) var(--space-xl);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-light);
        }

        .no-events-animation {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-calendar-star me-2"></i>
                Ukuqala Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="./">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="../user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="../user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="../admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="../auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Events Hero Section -->
    <section class="events-hero">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-3 fw-bold mb-4">Discover Amazing Events</h1>
                    <p class="lead mb-0">Find the perfect event for your interests and create unforgettable memories</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Filters Section -->
    <section class="py-4">
        <div class="container">
            <div class="filter-card p-4">
                <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search Events</label>
                    <input type="text" class="form-control form-control-modern"
                           id="search" name="search" value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Search by title or description">
                </div>

                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-control form-control-modern" id="category" name="category">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo htmlspecialchars($cat->category); ?>"
                                    <?php echo $category === $cat->category ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat->category); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="location" class="form-label">Location</label>
                    <select class="form-control form-control-modern" id="location" name="location">
                        <option value="">All Locations</option>
                        <?php foreach ($locations as $loc): ?>
                            <option value="<?php echo htmlspecialchars($loc->location); ?>"
                                    <?php echo $location === $loc->location ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($loc->location); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary-modern w-100">
                        <i class="fas fa-search me-2"></i>
                        Filter Events
                    </button>
                </div>
            </form>
            </div>
        </div>
    </section>

    <!-- Events Grid -->
    <section class="py-5">
        <div class="container">
            <!-- Results Info -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h4>
                        <?php if (!empty($search) || !empty($category) || !empty($location)): ?>
                            Search Results
                        <?php else: ?>
                            All Events
                        <?php endif ?>
                        <span class="text-muted">(<?php echo count($events); ?> events found)</span>
                    </h4>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary active" id="gridView">
                            <i class="fas fa-th"></i> Grid
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="listView">
                            <i class="fas fa-list"></i> List
                        </button>
                    </div>
                </div>
            </div>

            <!-- Events Container -->
            <div class="row events-container" id="eventsContainer">
                <?php if (!empty($events)): ?>
                    <?php foreach ($events as $index => $event): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="event-card-enhanced animate-on-scroll"
                                 data-event-id="<?php echo $event->id; ?>"
                                 data-category="<?php echo $event->category; ?>"
                                 style="animation-delay: <?php echo $index * 0.1; ?>s;">

                                <div class="event-image-container">
                                    <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>"
                                         alt="<?php echo htmlspecialchars($event->title); ?>"
                                         class="event-image">

                                    <div class="event-badge">
                                        <?php echo htmlspecialchars($event->category); ?>
                                    </div>

                                    <div class="event-price-tag">
                                        <?php echo formatCurrency($event->price); ?>
                                    </div>

                                    <?php if ($event->available_tickets < 10): ?>
                                        <div class="position-absolute top-0 start-0 m-3">
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-fire me-1"></i>Almost Sold Out
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="event-content">
                                    <h5 class="event-title"><?php echo htmlspecialchars($event->title); ?></h5>

                                    <div class="event-meta">
                                        <div class="event-meta-item">
                                            <i class="fas fa-calendar-alt"></i>
                                            <span><?php echo formatDate($event->event_date); ?></span>
                                        </div>
                                        <div class="event-meta-item">
                                            <i class="fas fa-clock"></i>
                                            <span><?php echo formatTime($event->event_time); ?></span>
                                        </div>
                                        <div class="event-meta-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span><?php echo htmlspecialchars($event->venue); ?></span>
                                        </div>
                                    </div>

                                    <p class="event-description">
                                        <?php echo htmlspecialchars(substr($event->description, 0, 120)) . '...'; ?>
                                    </p>

                                    <div class="event-actions">
                                        <div class="tickets-remaining <?php echo $event->available_tickets < 10 ? 'low' : ($event->available_tickets == 0 ? 'sold-out' : ''); ?>">
                                            <i class="fas fa-ticket-alt me-1"></i>
                                            <?php if ($event->available_tickets == 0): ?>
                                                Sold Out
                                            <?php else: ?>
                                                <?php echo $event->available_tickets; ?> left
                                            <?php endif; ?>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="details.php?id=<?php echo $event->id; ?>"
                                               class="btn btn-secondary-enhanced btn-sm">
                                                <i class="fas fa-eye me-1"></i>
                                                Details
                                            </a>
                                            <?php if ($event->available_tickets > 0): ?>
                                                <button class="btn btn-primary-enhanced btn-sm add-to-cart-btn"
                                                        data-event-id="<?php echo $event->id; ?>">
                                                    <i class="fas fa-cart-plus me-1"></i>
                                                    Add to Cart
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-outline-secondary btn-sm" disabled>
                                                    <i class="fas fa-times me-1"></i>
                                                    Sold Out
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="no-events-state">
                            <div class="no-events-animation">
                                <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                            </div>
                            <h3 class="fw-bold mb-3">No Events Found</h3>
                            <p class="text-muted mb-4">We couldn't find any events matching your criteria. Try adjusting your filters or explore our featured events.</p>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="./" class="btn btn-primary-enhanced btn-lg">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    Browse All Events
                                </a>
                                <a href="search.php" class="btn btn-secondary-enhanced btn-lg">
                                    <i class="fas fa-search me-2"></i>
                                    Advanced Search
                                </a>
                            </div>

                            <!-- Popular Categories -->
                            <div class="mt-5">
                                <h6 class="text-muted mb-3">Try These Popular Categories</h6>
                                <div class="d-flex flex-wrap justify-content-center gap-2">
                                    <a href="?category=Concert" class="badge bg-light text-dark px-3 py-2 text-decoration-none">
                                        <i class="fas fa-music me-1"></i>Concerts
                                    </a>
                                    <a href="?category=Conference" class="badge bg-light text-dark px-3 py-2 text-decoration-none">
                                        <i class="fas fa-briefcase me-1"></i>Conferences
                                    </a>
                                    <a href="?category=Workshop" class="badge bg-light text-dark px-3 py-2 text-decoration-none">
                                        <i class="fas fa-graduation-cap me-1"></i>Workshops
                                    </a>
                                    <a href="?category=Entertainment" class="badge bg-light text-dark px-3 py-2 text-decoration-none">
                                        <i class="fas fa-theater-masks me-1"></i>Entertainment
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <nav aria-label="Events pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&location=<?php echo urlencode($location); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&location=<?php echo urlencode($location); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&location=<?php echo urlencode($location); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Toast Container -->
    <div class="toast-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        // View toggle functionality
        document.getElementById('gridView').addEventListener('click', function() {
            document.getElementById('eventsContainer').className = 'row events-container';
            this.classList.add('active');
            document.getElementById('listView').classList.remove('active');
        });

        document.getElementById('listView').addEventListener('click', function() {
            document.getElementById('eventsContainer').className = 'events-container';
            this.classList.add('active');
            document.getElementById('gridView').classList.remove('active');
        });

        // Add to cart functionality
        document.addEventListener('click', function(e) {
            if (e.target.closest('.add-to-cart-btn')) {
                e.preventDefault();
                const button = e.target.closest('.add-to-cart-btn');
                const eventId = button.getAttribute('data-event-id');

                if (!eventId) {
                    showToast('Error: Event ID not found', 'error');
                    return;
                }

                // Check if user is logged in
                <?php if (!isLoggedIn()): ?>
                    showToast('Please login to add items to cart', 'warning');
                    setTimeout(() => {
                        window.location.href = '../auth/login.php?redirect=' + encodeURIComponent(window.location.href);
                    }, 1500);
                    return;
                <?php endif; ?>

                // Show loading state
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';
                button.disabled = true;

                // Make AJAX request to add to cart
                fetch('../api/add-to-cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        event_id: eventId,
                        quantity: 1
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('Event added to cart successfully!', 'success');
                        // Update cart count if element exists
                        const cartCount = document.querySelector('.cart-count');
                        if (cartCount && data.cart_count) {
                            cartCount.textContent = data.cart_count;
                            cartCount.style.display = 'inline';
                        }
                    } else {
                        showToast(data.message || 'Failed to add event to cart', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('An error occurred. Please try again.', 'error');
                })
                .finally(() => {
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        });
    </script>
</body>
</html>
