<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$pageTitle = 'My Bookings';
$userId = $_SESSION['user_id'];

// Get user bookings with event details
$db->query('SELECT b.*, e.title, e.description, e.event_date, e.event_time, e.venue, e.category, e.image_url
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           WHERE b.user_id = :user_id
           ORDER BY b.created_at DESC');
$db->bind(':user_id', $userId);
$bookings = $db->resultset();

// Get booking statistics
$db->query('SELECT 
    COUNT(*) as total_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN 1 ELSE 0 END) as confirmed_bookings,
    SUM(CASE WHEN booking_status = "pending" THEN 1 ELSE 0 END) as pending_bookings,
    SUM(CASE WHEN booking_status = "cancelled" THEN 1 ELSE 0 END) as cancelled_bookings,
    SUM(CASE WHEN booking_status = "confirmed" THEN total_amount ELSE 0 END) as total_spent,
    SUM(CASE WHEN booking_status = "confirmed" THEN quantity ELSE 0 END) as total_tickets
    FROM bookings 
    WHERE user_id = :user_id');
$db->bind(':user_id', $userId);
$stats = $db->single();

// Get upcoming events
$db->query('SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.image_url
           FROM bookings b
           JOIN events e ON b.event_id = e.id
           WHERE b.user_id = :user_id 
           AND e.event_date >= CURDATE()
           AND b.booking_status = "confirmed"
           ORDER BY e.event_date ASC
           LIMIT 3');
$db->bind(':user_id', $userId);
$upcomingEvents = $db->resultset();

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Ukuqala Events</title>

    <!-- African-Inspired Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Playfair+Display:wght@400;500;600;700;800&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--hero-gradient);
            background-attachment: fixed;
            min-height: 100vh;
        }

        .main-content {
            padding-top: 120px;
            padding-bottom: 60px;
        }

        .bookings-hero {
            background: var(--earth-gradient);
            color: white;
            padding: 4rem 0 3rem;
            margin-bottom: 3rem;
            border-radius: var(--border-radius-lg);
            position: relative;
            overflow: hidden;
        }

        .bookings-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--pattern-tribal);
            opacity: 0.1;
        }

        .booking-card {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            margin-bottom: 2rem;
            transition: var(--transition-cultural);
            overflow: hidden;
            position: relative;
        }

        .booking-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--sunset-gradient);
        }

        .booking-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-strong);
            border-color: var(--accent-color);
        }

        .booking-status {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 700;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-confirmed {
            background: var(--forest-gradient);
            color: white;
        }

        .status-pending {
            background: var(--sunset-gradient);
            color: white;
        }

        .status-cancelled {
            background: #dc3545;
            color: white;
        }

        .upcoming-event-card {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: var(--transition-cultural);
            position: relative;
            overflow: hidden;
        }

        .upcoming-event-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--pattern-geometric);
            opacity: 0.1;
        }

        .upcoming-event-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-cultural);
        }

        .no-bookings {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 4rem 2rem;
            text-align: center;
            box-shadow: var(--shadow-cultural);
        }

        .booking-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: var(--border-radius);
        }

        .booking-meta {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .booking-meta i {
            color: var(--accent-color);
            margin-right: 0.5rem;
            width: 16px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-drum me-2"></i>
                Ukuqala Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item active" href="bookings.php">
                                <i class="fas fa-ticket-alt me-2"></i>My Bookings
                            </a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">
                                    <i class="fas fa-cog me-2"></i>Admin Panel
                                </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Bookings Hero Section -->
            <div class="bookings-hero">
                <div class="text-center">
                    <h1 class="display-3 fw-bold mb-3">
                        <i class="fas fa-ticket-alt me-3"></i>
                        My Event Journey
                    </h1>
                    <p class="lead mb-0">
                        Track your bookings and relive your amazing experiences
                    </p>
                </div>
            </div>

            <?php if ($flashMessage): ?>
                <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                    <?php echo $flashMessage['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Booking Statistics -->
            <div class="row mb-5">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stat-icon bg-primary text-white mx-auto">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats->total_bookings ?? 0); ?></div>
                        <div class="stat-label">Total Bookings</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stat-icon bg-success text-white mx-auto">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats->confirmed_bookings ?? 0); ?></div>
                        <div class="stat-label">Confirmed Events</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stat-icon bg-warning text-white mx-auto">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats->total_tickets ?? 0); ?></div>
                        <div class="stat-label">Total Tickets</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card">
                        <div class="stat-icon bg-info text-white mx-auto">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-number"><?php echo formatCurrency($stats->total_spent ?? 0); ?></div>
                        <div class="stat-label">Total Spent</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Bookings List -->
                <div class="col-lg-8 mb-4">
                    <h3 class="section-title">
                        <i class="fas fa-list-ul"></i>
                        Booking History
                    </h3>

                    <?php if (!empty($bookings)): ?>
                        <?php foreach ($bookings as $booking): ?>
                            <div class="booking-card">
                                <div class="row align-items-center p-3">
                                    <div class="col-md-3">
                                        <img src="<?php echo $booking->image_url ?: '../images/africa1.png'; ?>"
                                             alt="<?php echo htmlspecialchars($booking->title); ?>"
                                             class="booking-image">
                                    </div>
                                    <div class="col-md-5">
                                        <h5 class="fw-bold mb-2 text-cultural">
                                            <?php echo htmlspecialchars($booking->title); ?>
                                        </h5>
                                        <div class="booking-meta">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?php echo formatDate($booking->event_date); ?>
                                        </div>
                                        <div class="booking-meta">
                                            <i class="fas fa-clock"></i>
                                            <?php echo formatTime($booking->event_time); ?>
                                        </div>
                                        <div class="booking-meta">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <?php echo htmlspecialchars($booking->venue); ?>
                                        </div>
                                        <div class="booking-meta">
                                            <i class="fas fa-tag"></i>
                                            <?php echo htmlspecialchars($booking->category); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <div class="fw-bold mb-1">
                                            <?php echo $booking->quantity; ?> ticket<?php echo $booking->quantity > 1 ? 's' : ''; ?>
                                        </div>
                                        <div class="price-display">
                                            <?php echo formatCurrency($booking->total_amount); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <div class="booking-status status-<?php echo $booking->booking_status; ?> mb-2">
                                            <?php echo ucfirst($booking->booking_status); ?>
                                        </div>
                                        <small class="text-muted">
                                            Booked: <?php echo formatDate($booking->created_at); ?>
                                        </small>
                                    </div>
                                </div>

                                <!-- Booking Actions -->
                                <div class="border-top p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                Booking ID: #<?php echo $booking->id; ?>
                                            </small>
                                        </div>
                                        <div>
                                            <?php if ($booking->booking_status === 'confirmed'): ?>
                                                <a href="../events/details.php?id=<?php echo $booking->event_id; ?>"
                                                   class="btn btn-outline-modern btn-sm me-2">
                                                    <i class="fas fa-eye me-1"></i>View Event
                                                </a>
                                                <button class="btn btn-accent-modern btn-sm"
                                                        onclick="downloadTicket(<?php echo $booking->id; ?>)">
                                                    <i class="fas fa-download me-1"></i>Download Ticket
                                                </button>
                                            <?php elseif ($booking->booking_status === 'pending'): ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>Awaiting confirmation
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="no-bookings">
                            <div class="mb-4">
                                <i class="fas fa-calendar-times fa-5x text-primary mb-3"></i>
                            </div>
                            <h3 class="fw-bold mb-3 text-cultural">No Bookings Yet</h3>
                            <p class="lead text-muted mb-4">
                                Your event journey is waiting to begin!<br>
                                Discover amazing events and create unforgettable memories.
                            </p>
                            <div class="d-grid gap-3 d-md-flex justify-content-md-center">
                                <a href="../events/" class="btn btn-primary-modern btn-lg">
                                    <i class="fas fa-calendar-star me-2"></i>Browse Events
                                </a>
                                <a href="../events/search.php" class="btn btn-accent-modern btn-lg">
                                    <i class="fas fa-search me-2"></i>Search Events
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Upcoming Events -->
                    <?php if (!empty($upcomingEvents)): ?>
                        <div class="dashboard-card mb-4">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-calendar-week me-2"></i>
                                Upcoming Events
                            </h5>
                            <?php foreach ($upcomingEvents as $event): ?>
                                <div class="upcoming-event-card">
                                    <h6 class="fw-bold mb-2"><?php echo htmlspecialchars($event->title); ?></h6>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small>
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo formatDate($event->event_date); ?>
                                            </small><br>
                                            <small>
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo formatTime($event->event_time); ?>
                                            </small>
                                        </div>
                                        <a href="../events/details.php?id=<?php echo $event->event_id; ?>"
                                           class="btn btn-outline-modern btn-sm">
                                            View
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Quick Actions -->
                    <div class="dashboard-card mb-4">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h5>
                        <div class="d-grid gap-2">
                            <a href="../events/" class="btn btn-primary-modern">
                                <i class="fas fa-calendar-plus me-2"></i>Book New Event
                            </a>
                            <a href="../booking/cart.php" class="btn btn-secondary-modern">
                                <i class="fas fa-shopping-cart me-2"></i>View Cart
                            </a>
                            <a href="profile.php" class="btn btn-outline-modern">
                                <i class="fas fa-user-edit me-2"></i>Edit Profile
                            </a>
                        </div>
                    </div>

                    <!-- Support -->
                    <div class="help-card">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-headset me-2"></i>
                            Need Help?
                        </h5>
                        <p class="mb-3">Questions about your bookings? We're here to help!</p>
                        <div class="d-grid gap-2">
                            <a href="mailto:<EMAIL>" class="btn btn-outline-modern">
                                <i class="fas fa-envelope me-2"></i>Email Support
                            </a>
                            <a href="tel:+27123456789" class="btn btn-outline-modern">
                                <i class="fas fa-phone me-2"></i>Call Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load cart count
            loadCartCount();

            // Navbar scroll effect
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar-modern');
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Add hover effects to booking cards
            const bookingCards = document.querySelectorAll('.booking-card');
            bookingCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // Download ticket function
        function downloadTicket(bookingId) {
            // Create a simple ticket download
            const ticketData = {
                bookingId: bookingId,
                timestamp: new Date().toISOString(),
                eventName: 'Ukuqala Events Ticket',
                message: 'Thank you for booking with Ukuqala Events!'
            };

            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(ticketData, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", `ukuqala-ticket-${bookingId}.json`);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();

            // Show success message
            alert('Ticket downloaded successfully! Please keep this for your records.');
        }

        // Load cart count function
        async function loadCartCount() {
            try {
                const response = await fetch('../booking/get_cart_count.php');
                const result = await response.json();

                if (result.success) {
                    const cartCountElement = document.querySelector('.cart-count');
                    if (result.cart_count > 0) {
                        cartCountElement.textContent = result.cart_count;
                        cartCountElement.style.display = 'block';
                    } else {
                        cartCountElement.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Failed to load cart count:', error);
            }
        }
    </script>
</body>
</html>
