# Ukuqala Events - Setup & Installation Guide

## 📋 Table of Contents
1. [Prerequisites](#prerequisites)
2. [System Requirements](#system-requirements)
3. [Installation Steps](#installation-steps)
4. [Database Setup with Docker](#database-setup-with-docker)
5. [Application Configuration](#application-configuration)
6. [Running the Application](#running-the-application)
7. [Testing the Setup](#testing-the-setup)
8. [Troubleshooting](#troubleshooting)
9. [Default Admin Account](#default-admin-account)

## 🔧 Prerequisites

Before setting up Ukuqala Events, ensure you have the following installed on your machine:

### Required Software
- **PHP 8.0 or higher** with the following extensions:
  - `php-mysqli` or `php-pdo-mysql`
  - `php-json`
  - `php-curl`
  - `php-mbstring`
  - `php-xml`
  - `php-zip`
- **Docker & Docker Compose** (for MySQL database)
- **Web Server** (Apache/Nginx) or **PHP Built-in Server**
- **Composer** (PHP dependency manager)
- **Git** (for cloning the repository)

### Operating System Support
- ✅ **Windows 10/11** (with WSL2 recommended for Docker)
- ✅ **macOS 10.15+**
- ✅ **Linux** (Ubuntu 18.04+, CentOS 7+, etc.)

### Windows-Specific Requirements
- **Docker Desktop for Windows** (includes Docker Compose)
- **PHP for Windows** (from php.net or XAMPP/WAMP)
- **PowerShell** or **Command Prompt**
- **WSL2** (recommended for better Docker performance)

## 💻 System Requirements

### Minimum Requirements
- **RAM**: 2GB
- **Storage**: 1GB free space
- **PHP Memory Limit**: 128MB
- **Network**: Internet connection for initial setup

### Recommended Requirements
- **RAM**: 4GB or more
- **Storage**: 2GB free space
- **PHP Memory Limit**: 256MB or higher

## 🚀 Installation Steps

### Step 1: Clone the Repository
```bash
# Clone the repository
git clone <repository-url> ukuqala-events
cd ukuqala-events

# Or if you have the zip file, extract it
unzip ukuqala-events.zip
cd ukuqala-events
```

### Step 2: Install PHP Dependencies (if using Composer)
```bash
# If composer.json exists, install dependencies
composer install
```

### Step 3: Set File Permissions

**For Linux/macOS:**
```bash
# Make sure web server can read/write necessary directories
chmod -R 755 .
chmod -R 777 uploads/ (if uploads directory exists)
chmod -R 777 logs/ (if logs directory exists)
```

**For Windows:**
```cmd
# Create necessary directories
mkdir uploads
mkdir logs
# Windows handles permissions automatically for local development
```

## 🐳 Database Setup with Docker

### Step 1: Create Docker Compose File
Create a `docker-compose.yml` file in your project root:

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: ukuqala_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword123
      MYSQL_DATABASE: ukuqala_events
      MYSQL_USER: ukuqala_user
      MYSQL_PASSWORD: ukuqala_pass123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: ukuqala_phpmyadmin
    restart: always
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: rootpassword123
    depends_on:
      - mysql

volumes:
  mysql_data:
```

### Step 2: Start Docker Services
```bash
# Start MySQL and phpMyAdmin containers
docker-compose up -d

# Check if containers are running
docker-compose ps
```

### Step 3: Wait for MySQL to Initialize
```bash
# Wait for MySQL to be ready (usually takes 30-60 seconds)
docker-compose logs mysql

# You should see "ready for connections" message
```

### Step 4: Import Database Schema
```bash
# Option 1: Using docker exec
docker exec -i ukuqala_mysql mysql -u ukuqala_user -pukuqala_pass123 ukuqala_events < database/schema.sql

# Option 2: Using phpMyAdmin
# Go to http://localhost:8080
# Login with: root / rootpassword123
# Select ukuqala_events database
# Import the SQL file from database/schema.sql
```

## ⚙️ Application Configuration

### Step 1: Configure Database Connection
Edit `includes/config.php`:

```php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'ukuqala_events');
define('DB_USER', 'ukuqala_user');
define('DB_PASS', 'ukuqala_pass123');
```

### Step 2: Configure Application Settings
In `includes/config.php`, update these settings:

```php
// Site Configuration
define('SITE_NAME', 'Ukuqala Events');
define('SITE_URL', 'http://localhost:8000'); // Adjust port as needed
define('ADMIN_EMAIL', '<EMAIL>');

// Currency Settings
define('APP_CURRENCY', 'ZAR');
define('APP_CURRENCY_SYMBOL', 'R');

// Email Configuration (optional)
define('SMTP_HOST', 'your-smtp-host');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Ukuqala Events');
```

## 🪟 Windows-Specific Setup Guide

### Prerequisites for Windows

#### Option 1: Docker Desktop (Recommended)
1. **Download Docker Desktop**: https://docs.docker.com/desktop/windows/
2. **Install Docker Desktop** with WSL2 backend
3. **Enable WSL2** if not already enabled:
   ```powershell
   # Run in PowerShell as Administrator
   wsl --install
   # Restart computer after installation
   ```

#### Option 2: XAMPP/WAMP Stack
1. **Download XAMPP**: https://www.apachefriends.org/download.html
2. **Install XAMPP** with PHP 8.0+ and MySQL
3. **Start Apache and MySQL** services

### Windows Installation Steps

#### Using Docker Desktop (Recommended)
```cmd
# 1. Clone or extract the project
cd C:\your-projects\
# Extract ukuqala-events.zip or clone repository

# 2. Navigate to project directory
cd ukuqala-events

# 3. Run the Windows setup script
quick-start.bat

# 4. Follow the prompts
```

#### Using XAMPP/WAMP
```cmd
# 1. Copy project to htdocs
copy ukuqala-events C:\xampp\htdocs\

# 2. Start XAMPP Control Panel
# Start Apache and MySQL services

# 3. Create database
# Open http://localhost/phpmyadmin
# Create database: ukuqala_events
# Import database/schema.sql

# 4. Configure application
# Edit includes/config.php with XAMPP settings:
# DB_HOST: localhost
# DB_USER: root
# DB_PASS: (empty for XAMPP)
# DB_NAME: ukuqala_events

# 5. Access application
# http://localhost/ukuqala-events/
```

### Windows Troubleshooting

#### Docker Issues
```cmd
# Check Docker status
docker --version
docker-compose --version

# Restart Docker Desktop
# Right-click Docker Desktop icon → Restart

# Check WSL2
wsl --list --verbose
# Should show WSL2 as version 2
```

#### XAMPP Issues
```cmd
# Check if ports are available
netstat -an | findstr :80
netstat -an | findstr :3306

# Change ports if needed in XAMPP Control Panel
# Apache: 80 → 8080
# MySQL: 3306 → 3307
```

#### PHP Issues
```cmd
# Check PHP installation
php --version

# Install PHP extensions (if using standalone PHP)
# Download from: https://windows.php.net/downloads/
# Enable extensions in php.ini:
# extension=mysqli
# extension=curl
# extension=mbstring
```

### Windows File Paths
```cmd
# Project structure on Windows
C:\ukuqala-events\
├── admin\
├── assets\
├── auth\
├── booking\
├── database\
├── events\
├── includes\
├── user\
├── index.php
├── quick-start.bat
└── docker-compose.yml
```

## 🏃‍♂️ Running the Application

### Option 1: Using PHP Built-in Server (Recommended for Development)
```bash
# Navigate to project directory
cd ukuqala-events

# Start PHP development server
php -S localhost:8000

# Application will be available at: http://localhost:8000
```

### Option 2: Using Apache/Nginx
```bash
# For Apache (Ubuntu/Debian)
sudo cp -r ukuqala-events /var/www/html/
sudo chown -R www-data:www-data /var/www/html/ukuqala-events
sudo systemctl restart apache2

# Application will be available at: http://localhost/ukuqala-events
```

### Option 3: Using XAMPP/WAMP/MAMP
1. Copy the `ukuqala-events` folder to your XAMPP/WAMP/MAMP `htdocs` directory
2. Start Apache and MySQL services
3. Access via: `http://localhost/ukuqala-events`

## 🧪 Testing the Setup

### Step 1: Verify Database Connection
1. Open your browser and go to: `http://localhost:8000` (or your configured URL)
2. You should see the Ukuqala Events homepage
3. If you see database connection errors, check your configuration

### Step 2: Test User Registration
1. Click "Register" and create a new user account
2. Verify you can log in successfully
3. Check that user dashboard is accessible

### Step 3: Test Admin Access
1. Use the default admin credentials (see below)
2. Access admin panel at: `http://localhost:8000/admin/`
3. Verify all admin features work correctly

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Database Connection Failed
```bash
# Check if MySQL container is running
docker-compose ps

# Check MySQL logs
docker-compose logs mysql

# Restart containers
docker-compose restart
```

#### Permission Denied Errors
```bash
# Fix file permissions (Linux/macOS)
chmod -R 755 .
chown -R www-data:www-data . (for Apache)
```

#### PHP Extensions Missing
```bash
# Ubuntu/Debian
sudo apt-get install php-mysqli php-json php-curl php-mbstring

# CentOS/RHEL
sudo yum install php-mysqli php-json php-curl php-mbstring
```

#### Port Already in Use
**Linux/macOS:**
```bash
# Check what's using port 3306
sudo netstat -tulpn | grep 3306

# Use different port in docker-compose.yml
ports:
  - "3307:3306"  # Change to 3307 or another available port
```

**Windows:**
```cmd
# Check what's using port 3306
netstat -an | findstr :3306

# Use different port in docker-compose.yml
ports:
  - "3307:3306"  # Change to 3307 or another available port
```

#### Windows-Specific Issues

**Docker Desktop Not Starting:**
```cmd
# Enable Hyper-V (Windows 10 Pro/Enterprise)
# Run in PowerShell as Administrator:
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# For Windows 10 Home, ensure WSL2 is properly installed
wsl --install
wsl --set-default-version 2
```

**XAMPP MySQL Won't Start:**
```cmd
# Check if Windows MySQL service is running
net stop mysql
# Or disable MySQL service in services.msc

# Change XAMPP MySQL port
# Edit C:\xampp\mysql\bin\my.ini
# Change port=3306 to port=3307
```

**PHP Extensions Missing (Windows):**
```cmd
# Download PHP extensions from: https://windows.php.net/downloads/pecl/
# Or use XAMPP which includes most extensions

# Enable in php.ini:
extension=mysqli
extension=curl
extension=mbstring
extension=openssl
```

## 👤 Default Admin Account

After setting up the database, you can create an admin account or use these default credentials if they exist:

**Default Admin Login:**
- **Email**: `<EMAIL>`
- **Password**: `admin123`

**To create a new admin account manually:**
1. Register a normal user account
2. Access the database via phpMyAdmin (http://localhost:8080)
3. Go to the `users` table
4. Find your user record and change the `role` field from `user` to `admin`

## 📱 Accessing the Application

### User Interface
- **Homepage**: `http://localhost:8000/`
- **Events**: `http://localhost:8000/events/`
- **User Dashboard**: `http://localhost:8000/user/dashboard.php`
- **Login**: `http://localhost:8000/auth/login.php`
- **Register**: `http://localhost:8000/auth/register.php`

### Admin Interface
- **Admin Dashboard**: `http://localhost:8000/admin/`
- **Manage Events**: `http://localhost:8000/admin/events.php`
- **View Bookings**: `http://localhost:8000/admin/bookings.php`
- **Reports**: `http://localhost:8000/admin/reports.php`

### Database Management
- **phpMyAdmin**: `http://localhost:8080`

## 🎯 Next Steps

1. **Customize the application** by modifying the CSS and templates
2. **Add your events** through the admin panel
3. **Configure email settings** for booking confirmations
4. **Set up SSL certificate** for production deployment
5. **Configure backup strategy** for your database

## 📞 Support

If you encounter any issues during setup:
1. Check the troubleshooting section above
2. Verify all prerequisites are installed correctly
3. Ensure Docker containers are running properly
4. Check PHP error logs for detailed error messages

## 🔒 Security Considerations

### For Production Deployment
1. **Change default passwords** in docker-compose.yml
2. **Use environment variables** for sensitive data
3. **Enable HTTPS** with SSL certificates
4. **Configure firewall** to restrict database access
5. **Regular security updates** for all components

### Environment Variables Setup
Create a `.env` file for sensitive configuration:
```bash
# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=ukuqala_events
DB_USER=ukuqala_user
DB_PASS=your_secure_password

# Email
SMTP_HOST=your-smtp-host
SMTP_USERNAME=your-email
SMTP_PASSWORD=your-email-password

# Security
SESSION_SECRET=your_random_session_secret
```

## 📊 Database Schema Information

### Main Tables
- **users**: User accounts and authentication
- **events**: Event information and details
- **bookings**: Event bookings and reservations
- **categories**: Event categories
- **payments**: Payment transaction records

### Sample Data
The database includes sample events and categories to help you get started quickly.

## 🔄 Backup & Maintenance

### Database Backup
```bash
# Create backup
docker exec ukuqala_mysql mysqldump -u ukuqala_user -pukuqala_pass123 ukuqala_events > backup_$(date +%Y%m%d).sql

# Restore backup
docker exec -i ukuqala_mysql mysql -u ukuqala_user -pukuqala_pass123 ukuqala_events < backup_20241219.sql
```

### Regular Maintenance
1. **Monitor disk space** for Docker volumes
2. **Update Docker images** regularly
3. **Check application logs** for errors
4. **Test backup restoration** periodically

## 🌐 Production Deployment

### Using Docker for Production
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD_FILE: /run/secrets/mysql_root_password
      MYSQL_DATABASE: ukuqala_events
      MYSQL_USER: ukuqala_user
      MYSQL_PASSWORD_FILE: /run/secrets/mysql_password
    volumes:
      - mysql_data:/var/lib/mysql
    secrets:
      - mysql_root_password
      - mysql_password
    networks:
      - ukuqala_network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - php
    networks:
      - ukuqala_network

secrets:
  mysql_root_password:
    file: ./secrets/mysql_root_password.txt
  mysql_password:
    file: ./secrets/mysql_password.txt

networks:
  ukuqala_network:
    driver: bridge

volumes:
  mysql_data:
```

## 📋 Development Guidelines

### Code Structure
```
ukuqala-events/
├── admin/              # Admin panel pages
├── assets/             # CSS, JS, images
├── auth/               # Authentication pages
├── booking/            # Booking system
├── database/           # SQL schema and migrations
├── events/             # Event-related pages
├── includes/           # Configuration and functions
├── user/               # User dashboard
├── uploads/            # File uploads (create if needed)
└── index.php           # Homepage
```

### Adding New Features
1. Follow the existing code structure
2. Use the established CSS classes for consistency
3. Implement proper error handling
4. Add appropriate security measures
5. Test thoroughly before deployment

## 🎨 Customization Guide

### Changing Colors and Themes
Edit `assets/css/modern-ui.css`:
```css
:root {
    --primary-color: #your-color;
    --bg-primary: #your-bg-color;
    /* Modify other CSS variables as needed */
}
```

### Adding New Event Categories
1. Access admin panel
2. Add categories through the interface, or
3. Insert directly into the database `categories` table

### Email Template Customization
Email templates are located in the `includes/` directory and can be customized to match your branding.

## 🚀 Performance Optimization

### For High Traffic
1. **Enable PHP OPcache**
2. **Use Redis for sessions**
3. **Implement CDN for static assets**
4. **Database query optimization**
5. **Enable gzip compression**

### Monitoring
- Monitor MySQL performance
- Check PHP error logs
- Monitor disk space usage
- Set up uptime monitoring

---

**🎉 Congratulations! Your Ukuqala Events application should now be running successfully!**

For additional support or questions, please refer to the troubleshooting section or check the application logs for detailed error information.
