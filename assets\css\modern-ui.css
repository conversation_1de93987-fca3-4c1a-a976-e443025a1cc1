/* Ukuqala Events - Premium African-Inspired UI Framework */
/* Enhanced for Modern UX with Cultural Aesthetics */

:root {
    /* Spotify-Inspired Dark Theme Color Palette */
    --spotify-black: #000000;
    --spotify-dark-gray: #121212;
    --spotify-gray: #191414;
    --spotify-light-gray: #282828;
    --spotify-medium-gray: #404040;
    --spotify-text-gray: #b3b3b3;
    --spotify-white: #ffffff;
    --spotify-green: #1db954;
    --spotify-green-hover: #1ed760;
    --spotify-green-dark: #169c46;

    /* Primary Colors - Spotify Green Theme */
    --primary-color: #1db954; /* Spotify Green */
    --primary-dark: #169c46;
    --primary-light: #1ed760;
    --primary-lighter: #4caf50;

    /* Secondary Colors - Dark Grays */
    --secondary-color: #282828; /* Spotify Light Gray */
    --secondary-dark: #191414;
    --secondary-light: #404040;
    --secondary-lighter: #535353;

    /* Accent Colors - Spotify Inspired */
    --accent-color: #1db954; /* Spotify Green */
    --accent-dark: #169c46;
    --accent-light: #1ed760;
    --accent-purple: #8b5cf6; /* Purple accent */
    --accent-blue: #3b82f6; /* Blue accent */
    --accent-orange: #f59e0b; /* Orange accent */

    /* Background Colors */
    --bg-primary: #121212; /* Main dark background */
    --bg-secondary: #1a1a1a; /* Secondary dark background */
    --bg-tertiary: #2a2a2a; /* Card/panel background */
    --bg-elevated: #3a3a3a; /* Elevated elements */
    --bg-hover: #333333; /* Hover states */

    /* Text Colors */
    --text-primary: #ffffff; /* Primary white text */
    --text-secondary: #b3b3b3; /* Secondary gray text */
    --text-muted: #6a6a6a; /* Muted text */
    --text-disabled: #404040; /* Disabled text */

    /* Spotify-Inspired Gradients */
    --primary-gradient: linear-gradient(135deg, #1db954 0%, #1ed760 50%, #4caf50 100%);
    --dark-gradient: linear-gradient(135deg, #121212 0%, #191414 50%, #282828 100%);
    --card-gradient: linear-gradient(135deg, #282828 0%, #404040 50%, #535353 100%);
    --hero-gradient: linear-gradient(135deg, #121212 0%, #191414 25%, #282828 50%, #1db954 75%, #1ed760 100%);
    --accent-gradient: linear-gradient(135deg, #1db954 0%, #8b5cf6 50%, #3b82f6 100%);

    /* Glassmorphism - Dark Theme */
    --glass-bg: rgba(42, 42, 42, 0.9);
    --glass-border: rgba(29, 185, 84, 0.3);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.6);

    /* Cultural Pattern Overlays */
    --pattern-tribal: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D4A574' fill-opacity='0.1'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20zM0 20c0-11.046 8.954-20 20-20v40c-11.046 0-20-8.954-20-20z'/%3E%3C/g%3E%3C/svg%3E");
    --pattern-geometric: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23FF6B35' fill-opacity='0.08'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");

    /* Chart Colors - Spotify Theme */
    --chart-primary: #1db954;
    --chart-secondary: #8b5cf6;
    --chart-tertiary: #3b82f6;
    --chart-quaternary: #f59e0b;
    --chart-quinary: #ef4444;
    --chart-senary: #06b6d4;

    /* Typography - Spotify-Inspired Fonts */
    --font-primary: 'Circular', 'Helvetica Neue', 'Arial', sans-serif;
    --font-display: 'Circular', 'Helvetica Neue', 'Arial', sans-serif;
    --font-accent: 'Circular', 'Helvetica Neue', 'Arial', sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;

    /* Enhanced Spacing & Sizing - Spotify Style */
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-round: 50%;
    --shadow-soft: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.25);
    --shadow-strong: 0 16px 48px rgba(0, 0, 0, 0.35);
    --shadow-spotify: 0 8px 24px rgba(0, 0, 0, 0.5);

    /* Cultural Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: all 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-fast: all 0.2s ease-out;
    --transition-cultural: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);

    /* Enhanced Spacing System */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Enhanced Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;

    /* Enhanced Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* Global Styles with Spotify Dark Theme */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(29, 185, 84, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Cultural Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.display-1 { font-size: 4.5rem; font-weight: 800; }
.display-2 { font-size: 3.5rem; font-weight: 700; }
.display-3 { font-size: 2.8rem; font-weight: 600; }
.display-4 { font-size: 2.2rem; font-weight: 600; }

.text-accent { color: var(--accent-color); }
.text-cultural {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Glassmorphism Container */
.glass-container {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--glass-shadow);
}

/* Modern Spotify-Inspired Navigation */
.navbar-modern {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--bg-elevated);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-soft);
}

.navbar-modern.scrolled {
    background: var(--bg-secondary);
    padding: 0.8rem 0;
    border-bottom: 1px solid var(--primary-color);
    box-shadow: var(--shadow-medium);
}

.navbar-brand {
    font-family: var(--font-display);
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--text-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.navbar-brand:hover {
    color: var(--primary-color);
    transform: scale(1.05);
}

.navbar-brand i {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.nav-link {
    font-weight: 500;
    color: var(--text-secondary) !important;
    transition: all 0.2s ease;
    position: relative;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    margin: 0 0.2rem;
}

.nav-link:hover {
    color: var(--text-primary) !important;
    background: var(--bg-elevated);
}

.nav-link.active {
    color: var(--primary-color) !important;
    background: var(--bg-elevated);
}

/* Dropdown Menu Styling */
.dropdown-menu {
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--bg-elevated) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-spotify) !important;
    padding: 0.5rem 0 !important;
}

.dropdown-item {
    color: var(--text-secondary) !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: var(--bg-elevated) !important;
    color: var(--text-primary) !important;
}

.dropdown-divider {
    border-color: var(--bg-elevated) !important;
}

/* Hero Section */
.hero-modern {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

.hero-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
}

.hero-title {
    font-family: var(--font-display);
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: slideInUp 1s ease-out 0.2s both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modern Spotify-Inspired Buttons */
.btn-modern {
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius-xl);
    font-weight: 700;
    font-family: var(--font-primary);
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    text-transform: none;
    font-size: 0.9rem;
    min-width: 120px;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition-cultural);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-primary-modern {
    background: var(--primary-color);
    color: var(--spotify-black);
    box-shadow: var(--shadow-soft);
    border: 1px solid transparent;
    font-weight: 700;
}

.btn-primary-modern:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: var(--spotify-black);
}

.btn-secondary-modern {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--bg-elevated);
    box-shadow: var(--shadow-soft);
}

.btn-secondary-modern:hover {
    background: var(--bg-elevated);
    transform: translateY(-2px);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.btn-accent-modern {
    background: var(--accent-purple);
    color: white;
    box-shadow: var(--shadow-soft);
    border: 1px solid transparent;
}

.btn-accent-modern:hover {
    background: var(--accent-blue);
    transform: translateY(-2px);
    color: white;
}

.btn-outline-modern {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-modern:hover {
    background: var(--primary-color);
    color: var(--spotify-black);
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

/* Button Sizes */
.btn-sm { padding: 0.8rem 1.5rem; font-size: 0.9rem; min-width: 100px; }
.btn-lg { padding: 1.5rem 3rem; font-size: 1.1rem; min-width: 180px; }
.btn-xl { padding: 2rem 4rem; font-size: 1.3rem; min-width: 220px; }

/* Modern Spotify-Inspired Card System */
.card-modern {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--bg-elevated);
    box-shadow: var(--shadow-soft);
    transition: all 0.2s ease;
    overflow: hidden;
    position: relative;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-spotify);
    border-color: var(--primary-color);
    background: var(--bg-elevated);
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.card-modern::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--pattern-geometric);
    opacity: 0.02;
    pointer-events: none;
}

.card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    padding: 1.5rem 2rem;
    border-bottom: none;
    font-weight: 700;
    font-size: 1.2rem;
}

.card-footer {
    background: rgba(212, 165, 116, 0.1);
    border-top: 2px solid var(--primary-color);
    padding: 1.5rem 2rem;
}

/* Special Card Variants */
.card-cultural {
    background: var(--earth-gradient);
    color: white;
    border: 3px solid var(--accent-color);
}

.card-cultural .card-body {
    background: rgba(0, 0, 0, 0.1);
}

.card-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(30px);
    border: 2px solid var(--glass-border);
}

.card-elevated {
    box-shadow: var(--shadow-strong);
    transform: translateY(-8px);
}

/* Enhanced Event Cards */
.event-card {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    background: rgba(245, 245, 220, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-cultural);
    transition: var(--transition-cultural);
    cursor: pointer;
    border: 2px solid var(--glass-border);
}

.event-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: var(--shadow-strong);
    border-color: var(--accent-color);
}

.event-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition-cultural);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.event-card:hover .event-image {
    transform: scale(1.1);
}

.event-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: var(--sunset-gradient);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: var(--shadow-soft);
    border: 2px solid white;
}

.event-content {
    padding: 2rem;
    position: relative;
}

.event-title {
    font-family: var(--font-display);
    font-size: 1.4rem;
    font-weight: 800;
    margin-bottom: 1rem;
    color: var(--text-primary);
    line-height: 1.3;
}

.event-meta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
}

.event-meta i {
    color: var(--accent-color);
    margin-right: 0.5rem;
}

.event-price {
    font-size: 1.8rem;
    font-weight: 800;
    font-family: var(--font-display);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Profile Page Styles */
.profile-hero {
    background: var(--hero-gradient);
    padding: 6rem 0 4rem;
    position: relative;
    overflow: hidden;
}

.profile-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--pattern-tribal);
    opacity: 0.1;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: var(--sunset-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    box-shadow: var(--shadow-strong);
    border: 4px solid white;
    position: relative;
    z-index: 2;
}

.profile-card {
    background: var(--glass-bg);
    backdrop-filter: blur(30px);
    border: 2px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-cultural);
    overflow: hidden;
}

.quick-action-card {
    background: rgba(245, 245, 220, 0.9);
    backdrop-filter: blur(20px);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-cultural);
    height: 100%;
}

.quick-action-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-cultural);
    border-color: var(--accent-color);
}

.quick-action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

/* Cart Styles - Spotify Dark Theme */
.cart-item {
    background: var(--bg-tertiary);
    border: 1px solid var(--bg-elevated);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
    color: var(--text-primary);
}

.cart-item:hover {
    background: var(--bg-elevated);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.cart-summary {
    background: var(--bg-tertiary);
    border: 1px solid var(--bg-elevated);
    color: var(--text-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-spotify);
    position: sticky;
    top: 120px;
}

.cart-total {
    font-size: 2rem;
    font-weight: 700;
    font-family: var(--font-display);
    margin-bottom: 1rem;
    color: var(--primary-color);
}

/* Enhanced Spotify-Inspired Form Styling */
.form-modern {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--bg-elevated);
    position: relative;
    color: var(--text-primary);
}

.form-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.form-control-modern {
    border: 1px solid var(--bg-elevated);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    font-family: var(--font-primary);
    transition: all 0.2s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 400;
}

.form-control-modern:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.2);
    background: var(--bg-secondary);
    outline: none;
}

.form-control-modern::placeholder {
    color: var(--text-muted);
    font-style: normal;
}

.form-label {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
    display: block;
}

.form-group {
    margin-bottom: 2rem;
}

/* Dashboard-Specific Components - Spotify Style */
.dashboard-card {
    background: var(--bg-tertiary);
    border: 1px solid var(--bg-elevated);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    color: var(--text-primary);
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-spotify);
    border-color: var(--primary-color);
    background: var(--bg-elevated);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.stats-card {
    text-align: center;
    padding: 2rem;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--bg-elevated);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-soft);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-spotify);
    border-color: var(--primary-color);
    background: var(--bg-elevated);
}

/* Admin Dashboard Specific Styles - Spotify Theme */
.admin-welcome {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 4rem 0 3rem;
    margin-bottom: 3rem;
    border-radius: var(--border-radius-lg);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-spotify);
    border: 1px solid var(--bg-elevated);
}

.admin-welcome::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(29, 185, 84, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.admin-stats-card {
    background: var(--bg-tertiary);
    border: 1px solid var(--bg-elevated);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-soft);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
    color: var(--text-primary);
}

.admin-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.admin-stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-spotify);
    border-color: var(--primary-color);
    background: var(--bg-elevated);
}

.admin-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
}

.admin-stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    font-family: var(--font-display);
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.admin-stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chart-card {
    background: var(--bg-tertiary);
    border: 1px solid var(--bg-elevated);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-soft);
    overflow: hidden;
    color: var(--text-primary);
}

.chart-header {
    background: var(--bg-elevated);
    color: var(--text-primary);
    padding: 1.5rem;
    position: relative;
    border-bottom: 1px solid var(--bg-elevated);
}

.stats-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--pattern-tribal);
    opacity: 0.1;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    font-family: var(--font-display);
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    position: relative;
    z-index: 2;
}

/* Loading Animation */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Advanced Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Hover Effects */
.card-modern:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    z-index: 10000;
    border-radius: var(--border-radius);
    backdrop-filter: blur(20px);
    animation: slideInRight 0.3s ease-out;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Progress Indicators */
.progress-modern {
    height: 8px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.progress-modern .progress-bar {
    background: var(--primary-gradient);
    border-radius: 50px;
    transition: width 0.6s ease;
}

/* Advanced Form Styling */
.form-floating-modern {
    position: relative;
}

.form-floating-modern .form-control {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating-modern label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}

/* Table Enhancements */
.table-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-soft);
}

.table-modern th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.table-modern td {
    border: none;
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table-modern tbody tr {
    transition: var(--transition-smooth);
}

.table-modern tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* Badge Enhancements */
.badge-modern {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

/* Tooltip Styling */
.tooltip-modern {
    background: var(--dark-bg);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.85rem;
    box-shadow: var(--shadow-medium);
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Global Bootstrap Overrides for Dark Theme */
.table {
    color: var(--text-primary) !important;
}

.table-dark {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

.table-dark th,
.table-dark td,
.table th,
.table td {
    border-color: var(--bg-elevated) !important;
}

.table-dark thead th,
.table thead th {
    background-color: var(--bg-elevated) !important;
    border-color: var(--bg-elevated) !important;
    color: var(--text-primary) !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td,
.table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: var(--bg-secondary) !important;
}

.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
    background-color: var(--bg-elevated) !important;
}

.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
}

.badge.bg-success {
    background-color: var(--primary-color) !important;
    color: var(--spotify-black) !important;
}

.badge.bg-warning {
    background-color: var(--accent-orange) !important;
    color: var(--spotify-black) !important;
}

.badge.bg-danger {
    background-color: #ef4444 !important;
    color: white !important;
}

.badge.bg-info {
    background-color: var(--accent-blue) !important;
    color: white !important;
}

/* Improved Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-elevated);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Enhanced Focus States */
.form-control:focus,
.form-select:focus,
.btn:focus {
    box-shadow: 0 0 0 2px rgba(29, 185, 84, 0.25) !important;
    border-color: var(--primary-color) !important;
}

/* Improved Text Selection */
/* Bootstrap Component Overrides */
.modal-content {
    background-color: var(--bg-tertiary) !important;
    border: 1px solid var(--bg-elevated) !important;
    color: var(--text-primary) !important;
}

.modal-header {
    border-bottom: 1px solid var(--bg-elevated) !important;
}

.modal-footer {
    border-top: 1px solid var(--bg-elevated) !important;
}

.alert {
    border: 1px solid var(--bg-elevated) !important;
}

.alert-success {
    background-color: rgba(29, 185, 84, 0.1) !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1) !important;
    border-color: #ef4444 !important;
    color: #ef4444 !important;
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1) !important;
    border-color: var(--accent-orange) !important;
    color: var(--accent-orange) !important;
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1) !important;
    border-color: var(--accent-blue) !important;
    color: var(--accent-blue) !important;
}

.pagination .page-link {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--bg-elevated) !important;
    color: var(--text-secondary) !important;
}

.pagination .page-link:hover {
    background-color: var(--bg-elevated) !important;
    border-color: var(--primary-color) !important;
    color: var(--text-primary) !important;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: var(--spotify-black) !important;
}

::selection {
    background-color: var(--primary-color);
    color: var(--spotify-black);
}

::-moz-selection {
    background-color: var(--primary-color);
    color: var(--spotify-black);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container-fluid {
        padding: 0 2rem;
    }

    .stats-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 992px) {
    .navbar-brand {
        font-size: 1.6rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .dashboard-card {
        margin-bottom: 2rem;
    }

    .quick-action-card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 768px) {
    .navbar-modern {
        padding: 1rem 0;
    }

    .navbar-brand {
        font-size: 1.4rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn-modern {
        padding: 1rem 2rem;
        font-size: 0.95rem;
        min-width: 120px;
    }

    .event-card {
        margin-bottom: 2rem;
    }

    .form-modern {
        padding: 2rem;
    }

    .notification {
        min-width: 280px;
        right: 10px;
        left: 10px;
    }

    .stats-card {
        padding: 2rem 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .profile-hero {
        padding: 5rem 0 3rem;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
    }

    .cart-summary {
        position: static;
        margin-top: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .display-1 { font-size: 3rem; }
    .display-2 { font-size: 2.5rem; }
    .display-3 { font-size: 2rem; }
    .display-4 { font-size: 1.8rem; }

    .card-modern {
        margin-bottom: 1.5rem;
    }

    .btn-modern {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        min-width: 100px;
    }

    .form-modern {
        padding: 1.5rem;
    }

    .dashboard-card {
        padding: 1.5rem;
    }

    .event-content {
        padding: 1.5rem;
    }

    .event-title {
        font-size: 1.2rem;
    }

    .event-price {
        font-size: 1.5rem;
    }
}

/* Utility Classes */
.bg-primary-gradient { background: var(--primary-gradient); }
.bg-secondary-gradient { background: var(--secondary-gradient); }
.bg-sunset-gradient { background: var(--sunset-gradient); }
.bg-earth-gradient { background: var(--earth-gradient); }
.bg-forest-gradient { background: var(--forest-gradient); }

.text-primary-color { color: var(--primary-color); }
.text-secondary-color { color: var(--secondary-color); }
.text-accent-color { color: var(--accent-color); }
.text-tertiary-color { color: var(--tertiary-color); }

.border-primary { border-color: var(--primary-color) !important; }
.border-accent { border-color: var(--accent-color) !important; }
.border-secondary { border-color: var(--secondary-color) !important; }

.shadow-cultural { box-shadow: var(--shadow-cultural); }
.shadow-soft { box-shadow: var(--shadow-soft); }
.shadow-medium { box-shadow: var(--shadow-medium); }
.shadow-strong { box-shadow: var(--shadow-strong); }

.rounded-cultural { border-radius: var(--border-radius); }
.rounded-lg-cultural { border-radius: var(--border-radius-lg); }
.rounded-xl-cultural { border-radius: var(--border-radius-xl); }

.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(25px);
    border: 2px solid var(--glass-border);
}

.cultural-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--pattern-tribal);
    opacity: 0.05;
    pointer-events: none;
}

.hover-lift {
    transition: var(--transition-cultural);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-cultural);
}

.section-title {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 2rem;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background: var(--sunset-gradient);
    border-radius: 2px;
}

.section-title i {
    color: var(--accent-color);
    margin-right: 1rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

.bounce-in {
    animation: bounceIn 1s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Print Styles */
@media print {
    .navbar-modern,
    .btn-modern,
    .notification,
    .toast-container,
    .loading-overlay {
        display: none !important;
    }

    .card-modern {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .text-cultural,
    .text-accent-color {
        color: #333 !important;
    }
}

/* Enhanced Toast Notification System */
.toast-container {
    position: fixed;
    top: var(--space-lg);
    right: var(--space-lg);
    z-index: var(--z-toast);
    max-width: 400px;
}

.toast-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    border-left: 4px solid var(--primary-color);
    margin-bottom: var(--space-sm);
    padding: var(--space-lg);
    transform: translateX(100%);
    transition: var(--transition-spring);
    opacity: 0;
}

.toast-modern.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-modern.success {
    border-left-color: #10b981;
}

.toast-modern.error {
    border-left-color: #ef4444;
}

.toast-modern.warning {
    border-left-color: #f59e0b;
}

.toast-modern.info {
    border-left-color: var(--primary-color);
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xs);
}

.toast-title {
    font-weight: 600;
    font-size: var(--text-sm);
    color: #1f2937;
}

.toast-close {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: 50%;
    transition: var(--transition-fast);
}

.toast-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #374151;
}

.toast-body {
    font-size: var(--text-sm);
    color: #6b7280;
    line-height: 1.5;
}

/* Enhanced Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-smooth);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    text-align: center;
    color: var(--primary-color);
}

.loading-spinner-modern {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(64, 134, 129, 0.1);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--space-md);
}

.loading-text {
    font-size: var(--text-lg);
    font-weight: 500;
}

/* Enhanced Floating Labels */
.form-floating-modern {
    position: relative;
    margin-bottom: var(--space-lg);
}

.form-floating-modern .form-control-modern {
    padding: var(--space-lg) var(--space-md) var(--space-sm);
    border: 2px solid #e5e7eb;
    border-radius: var(--border-radius);
    background: white;
    font-size: var(--text-base);
    transition: var(--transition-smooth);
    width: 100%;
}

.form-floating-modern .form-control-modern:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(64, 134, 129, 0.1);
}

.form-floating-modern label {
    position: absolute;
    top: var(--space-lg);
    left: var(--space-md);
    font-size: var(--text-base);
    color: #6b7280;
    pointer-events: none;
    transition: var(--transition-smooth);
    transform-origin: left top;
}

.form-floating-modern .form-control-modern:focus + label,
.form-floating-modern .form-control-modern:not(:placeholder-shown) + label {
    transform: translateY(-1.5rem) scale(0.85);
    color: var(--primary-color);
    font-weight: 500;
}

/* Enhanced Button Styles */
.btn-modern-enhanced {
    position: relative;
    overflow: hidden;
    border: none;
    border-radius: var(--border-radius);
    padding: var(--space-md) var(--space-xl);
    font-weight: 600;
    font-size: var(--text-base);
    text-transform: none;
    letter-spacing: 0.025em;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    text-decoration: none;
}

.btn-modern-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-smooth);
}

.btn-modern-enhanced:hover::before {
    left: 100%;
}

.btn-primary-enhanced {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(64, 134, 129, 0.39);
}

.btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(64, 134, 129, 0.5);
    color: white;
}

.btn-secondary-enhanced {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 4px 14px 0 rgba(64, 134, 129, 0.15);
}

.btn-secondary-enhanced:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(64, 134, 129, 0.3);
}

/* Print Styles */
@media print {
    .navbar-modern,
    .btn-modern,
    .notification,
    .toast-container,
    .loading-overlay {
        display: none !important;
    }

    .card-modern {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    body {
        background: white !important;
        color: black !important;
    }
}
