<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Get event ID
$eventId = $_GET['id'] ?? 0;

if (!$eventId) {
    redirect('./');
}

// Get event details
$event = $eventManager->getEventById($eventId);

if (!$event) {
    redirect('./');
}

$pageTitle = $event->title;

// Get related events (same category)
$relatedEvents = $eventManager->getEventsByCategory($event->category);
$relatedEvents = array_filter($relatedEvents, function($e) use ($eventId) {
    return $e->id != $eventId;
});
$relatedEvents = array_slice($relatedEvents, 0, 3);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - Ukuqala Events</title>

    <!-- African-Inspired Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Playfair+Display:wght@400;500;600;700;800&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <!-- Meta tags for social sharing -->
    <meta property="og:title" content="<?php echo htmlspecialchars($event->title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars(substr($event->description, 0, 160)); ?>">
    <meta property="og:image" content="<?php echo $event->image_url; ?>">
    <meta property="og:type" content="event">

    <style>
        body {
            background: var(--hero-gradient);
            background-attachment: fixed;
            min-height: 100vh;
        }

        .main-content {
            padding-top: 120px;
            padding-bottom: 60px;
        }

        .event-hero {
            position: relative;
            height: 60vh;
            min-height: 500px;
            overflow: hidden;
            border-radius: var(--border-radius-lg);
            margin-bottom: 3rem;
            box-shadow: var(--shadow-strong);
        }

        .event-hero-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition-cultural);
        }

        .event-hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%);
            display: flex;
            align-items: end;
            padding: 3rem;
        }

        .event-hero-content {
            color: white;
            max-width: 600px;
        }

        .event-title {
            font-family: var(--font-display);
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1.2;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .event-subtitle {
            font-size: 1.3rem;
            font-weight: 500;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .event-badges {
            position: absolute;
            top: 2rem;
            left: 2rem;
            z-index: 10;
        }

        .event-badge {
            background: var(--sunset-gradient);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            font-weight: 700;
            margin-right: 1rem;
            margin-bottom: 1rem;
            display: inline-block;
            box-shadow: var(--shadow-soft);
            border: 2px solid white;
        }

        .event-details-card {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-cultural);
            overflow: hidden;
            position: relative;
        }

        .event-details-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--sunset-gradient);
        }

        .booking-card {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-cultural);
            position: sticky;
            top: 120px;
            overflow: hidden;
        }

        .booking-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--pattern-geometric);
            opacity: 0.1;
        }

        .booking-card-content {
            position: relative;
            z-index: 2;
            padding: 2.5rem;
        }

        .price-display {
            font-family: var(--font-display);
            font-size: 3rem;
            font-weight: 800;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .event-meta-item {
            background: rgba(245, 245, 220, 0.9);
            backdrop-filter: blur(20px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: var(--transition-cultural);
        }

        .event-meta-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-soft);
            border-color: var(--accent-color);
        }

        .event-meta-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 1rem;
            box-shadow: var(--shadow-soft);
        }

        .social-share-btn {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 2px solid var(--primary-color);
            color: var(--text-primary);
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            text-decoration: none;
            transition: var(--transition-cultural);
            margin: 0.5rem;
            display: inline-block;
        }

        .social-share-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
            box-shadow: var(--shadow-soft);
        }

        .related-event-card {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition-cultural);
            height: 100%;
        }

        .related-event-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-strong);
            border-color: var(--accent-color);
        }

        .related-event-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: var(--transition-cultural);
        }

        .related-event-card:hover .related-event-image {
            transform: scale(1.1);
        }

        .breadcrumb-modern {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .breadcrumb-modern .breadcrumb {
            margin: 0;
        }

        .breadcrumb-modern .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }

        .breadcrumb-modern .breadcrumb-item.active {
            color: var(--text-primary);
            font-weight: 700;
        }

        .event-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-secondary);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-family: var(--font-display);
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .quantity-selector {
            background: rgba(255, 255, 255, 0.9);
            border: 3px solid white;
            border-radius: var(--border-radius);
            padding: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .quantity-selector:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.15);
            outline: none;
        }

        @media (max-width: 768px) {
            .event-title {
                font-size: 2.5rem;
            }

            .event-hero {
                height: 50vh;
                min-height: 400px;
            }

            .event-hero-overlay {
                padding: 2rem;
            }

            .booking-card {
                position: static;
                margin-top: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-drum me-2"></i>
                Ukuqala Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="../booking/cart.php">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="../user/dashboard.php">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a></li>
                                <li><a class="dropdown-item" href="../user/profile.php">
                                    <i class="fas fa-user-edit me-2"></i>Profile
                                </a></li>
                                <li><a class="dropdown-item" href="../user/bookings.php">
                                    <i class="fas fa-ticket-alt me-2"></i>My Bookings
                                </a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="../admin/">
                                        <i class="fas fa-cog me-2"></i>Admin Panel
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="../auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Breadcrumb -->
            <div class="breadcrumb-modern">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../">Home</a></li>
                        <li class="breadcrumb-item"><a href="./">Events</a></li>
                        <li class="breadcrumb-item active"><?php echo htmlspecialchars($event->title); ?></li>
                    </ol>
                </nav>
            </div>

            <!-- Event Hero Section -->
            <div class="event-hero">
                <img src="<?php echo $event->image_url ?: '../images/africa1.png'; ?>"
                     alt="<?php echo htmlspecialchars($event->title); ?>"
                     class="event-hero-image">

                <div class="event-badges">
                    <span class="event-badge">
                        <i class="fas fa-tag me-2"></i><?php echo htmlspecialchars($event->category); ?>
                    </span>
                    <?php if ($event->available_tickets < 10): ?>
                        <span class="event-badge" style="background: var(--forest-gradient);">
                            <i class="fas fa-fire me-2"></i>Almost Sold Out
                        </span>
                    <?php endif; ?>
                    <?php if (strtotime($event->event_date) <= strtotime('+7 days')): ?>
                        <span class="event-badge" style="background: var(--secondary-gradient);">
                            <i class="fas fa-clock me-2"></i>Coming Soon
                        </span>
                    <?php endif; ?>
                </div>

                <div class="event-hero-overlay">
                    <div class="event-hero-content">
                        <h1 class="event-title"><?php echo htmlspecialchars($event->title); ?></h1>
                        <p class="event-subtitle">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <?php echo formatDate($event->event_date); ?> at <?php echo formatTime($event->event_time); ?>
                            <br>
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <?php echo htmlspecialchars($event->venue); ?>, <?php echo htmlspecialchars($event->location); ?>
                        </p>
                        <div class="d-flex gap-3 flex-wrap">
                            <span class="badge bg-light text-dark px-3 py-2 fs-6">
                                <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($event->organizer); ?>
                            </span>
                            <span class="badge bg-light text-dark px-3 py-2 fs-6">
                                <i class="fas fa-ticket-alt me-2"></i><?php echo $event->available_tickets; ?> tickets left
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Event Details -->
                <div class="col-lg-8 mb-4">
                    <div class="event-details-card">
                        <div class="card-body p-4">
                            <!-- Event Meta Info -->
                            <div class="row mb-5">
                                <div class="col-md-6 mb-3">
                                    <div class="event-meta-item">
                                        <div class="d-flex align-items-center">
                                            <div class="event-meta-icon">
                                                <i class="fas fa-calendar-alt"></i>
                                            </div>
                                            <div>
                                                <h6 class="fw-bold mb-1 text-cultural">Date & Time</h6>
                                                <p class="mb-0 text-muted">
                                                    <?php echo formatDate($event->event_date); ?><br>
                                                    <strong><?php echo formatTime($event->event_time); ?></strong>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="event-meta-item">
                                        <div class="d-flex align-items-center">
                                            <div class="event-meta-icon">
                                                <i class="fas fa-map-marker-alt"></i>
                                            </div>
                                            <div>
                                                <h6 class="fw-bold mb-1 text-cultural">Location</h6>
                                                <p class="mb-0 text-muted">
                                                    <?php echo htmlspecialchars($event->venue); ?><br>
                                                    <strong><?php echo htmlspecialchars($event->location); ?></strong>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="event-meta-item">
                                        <div class="d-flex align-items-center">
                                            <div class="event-meta-icon">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <h6 class="fw-bold mb-1 text-cultural">Organizer</h6>
                                                <p class="mb-0 text-muted">
                                                    <?php echo htmlspecialchars($event->organizer); ?><br>
                                                    <strong><?php echo htmlspecialchars($event->organizer_contact); ?></strong>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="event-meta-item">
                                        <div class="d-flex align-items-center">
                                            <div class="event-meta-icon">
                                                <i class="fas fa-ticket-alt"></i>
                                            </div>
                                            <div>
                                                <h6 class="fw-bold mb-1 text-cultural">Availability</h6>
                                                <p class="mb-0 text-muted">
                                                    <?php echo $event->available_tickets; ?> of <?php echo $event->total_tickets; ?> tickets<br>
                                                    <strong><?php echo round(($event->total_tickets - $event->available_tickets) / $event->total_tickets * 100); ?>% sold</strong>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Event Description -->
                            <div class="mb-5">
                                <h3 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    About This Cultural Experience
                                </h3>
                                <div class="event-description">
                                    <?php echo nl2br(htmlspecialchars($event->description)); ?>
                                </div>
                            </div>

                            <!-- Social Sharing -->
                            <div class="mb-4">
                                <h4 class="section-title">
                                    <i class="fas fa-share-alt"></i>
                                    Share the Ubuntu Spirit
                                </h4>
                                <p class="text-muted mb-3">Spread the word about this amazing cultural event!</p>
                                <div class="social-share text-center">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(SITE_URL . '/events/details.php?id=' . $event->id); ?>"
                                       target="_blank" class="social-share-btn">
                                        <i class="fab fa-facebook-f me-2"></i>Facebook
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?text=<?php echo urlencode($event->title); ?>&url=<?php echo urlencode(SITE_URL . '/events/details.php?id=' . $event->id); ?>"
                                       target="_blank" class="social-share-btn">
                                        <i class="fab fa-twitter me-2"></i>Twitter
                                    </a>
                                    <a href="https://wa.me/?text=<?php echo urlencode($event->title . ' - ' . SITE_URL . '/events/details.php?id=' . $event->id); ?>"
                                       target="_blank" class="social-share-btn">
                                        <i class="fab fa-whatsapp me-2"></i>WhatsApp
                                    </a>
                                    <a href="mailto:?subject=<?php echo urlencode($event->title); ?>&body=<?php echo urlencode('Check out this amazing event: ' . $event->title . ' - ' . SITE_URL . '/events/details.php?id=' . $event->id); ?>"
                                       class="social-share-btn">
                                        <i class="fas fa-envelope me-2"></i>Email
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Sidebar -->
                <div class="col-lg-4">
                    <div class="booking-card">
                        <div class="booking-card-content">
                            <div class="text-center mb-4">
                                <h2 class="price-display mb-2"><?php echo formatCurrency($event->price); ?></h2>
                                <p class="mb-0 opacity-90">per ticket</p>
                            </div>

                            <?php if ($event->available_tickets > 0): ?>
                                <div class="booking-form" data-event-id="<?php echo $event->id; ?>">
                                    <div class="mb-4">
                                        <label for="quantity" class="form-label fw-bold text-white">Number of Tickets</label>
                                        <select class="quantity-selector w-100" id="quantity" name="quantity">
                                            <?php for ($i = 1; $i <= min(10, $event->available_tickets); $i++): ?>
                                                <option value="<?php echo $i; ?>"><?php echo $i; ?> ticket<?php echo $i > 1 ? 's' : ''; ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>

                                    <div class="mb-4 p-3" style="background: rgba(255,255,255,0.1); border-radius: var(--border-radius);">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="fw-bold">Subtotal:</span>
                                            <span class="fw-bold fs-5 subtotal"><?php echo formatCurrency($event->price); ?></span>
                                        </div>
                                        <small class="opacity-75">All fees included</small>
                                    </div>

                                    <?php if (isLoggedIn()): ?>
                                        <div class="d-grid gap-3">
                                            <button class="btn btn-accent-modern btn-lg add-to-cart-btn">
                                                <i class="fas fa-cart-plus me-2"></i>
                                                Add to Cart
                                            </button>
                                            <a href="../booking/checkout.php?event_id=<?php echo $event->id; ?>&quantity=1"
                                               class="btn btn-secondary-modern quick-book-btn">
                                                <i class="fas fa-bolt me-2"></i>
                                                Quick Book Now
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="d-grid gap-3">
                                            <a href="../auth/login.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>"
                                               class="btn btn-accent-modern btn-lg">
                                                <i class="fas fa-sign-in-alt me-2"></i>
                                                Login to Book
                                            </a>
                                            <a href="../auth/register.php" class="btn btn-outline-modern">
                                                <i class="fas fa-user-plus me-2"></i>
                                                Create Account
                                            </a>
                                        </div>
                                        <p class="text-center mt-3 opacity-75">
                                            Join the Ubuntu community and start your cultural journey!
                                        </p>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center">
                                    <div class="alert alert-warning mb-4">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        This event is sold out
                                    </div>
                                    <button class="btn btn-outline-modern" disabled>
                                        <i class="fas fa-times me-2"></i>
                                        Sold Out
                                    </button>
                                    <p class="mt-3 opacity-75">
                                        Don't miss out next time! Follow us for updates.
                                    </p>
                                </div>
                            <?php endif; ?>

                            <!-- Event Stats -->
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $event->total_tickets - $event->available_tickets; ?></div>
                                    <small>Tickets Sold</small>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $event->available_tickets; ?></div>
                                    <small>Available</small>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo round(($event->total_tickets - $event->available_tickets) / $event->total_tickets * 100); ?>%</div>
                                    <small>Sold</small>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo ceil((strtotime($event->event_date) - time()) / (60 * 60 * 24)); ?></div>
                                    <small>Days Left</small>
                                </div>
                            </div>

                            <!-- Security Info -->
                            <div class="mt-4 pt-3 border-top border-light text-center">
                                <small class="opacity-75">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Secure booking with SSL encryption
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Events -->
            <?php if (!empty($relatedEvents)): ?>
                <div class="row mt-5">
                    <div class="col-12">
                        <h3 class="section-title">
                            <i class="fas fa-calendar-week"></i>
                            More Cultural Experiences
                        </h3>
                        <p class="text-muted mb-4">Discover other amazing events in the <?php echo htmlspecialchars($event->category); ?> category</p>
                    </div>
                    <?php foreach ($relatedEvents as $relatedEvent): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="related-event-card" data-event-id="<?php echo $relatedEvent->id; ?>">
                                <div class="position-relative overflow-hidden">
                                    <img src="<?php echo $relatedEvent->image_url ?: '../images/africa2.png'; ?>"
                                         alt="<?php echo htmlspecialchars($relatedEvent->title); ?>"
                                         class="related-event-image">
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-primary">
                                            <?php echo htmlspecialchars($relatedEvent->category); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="p-3">
                                    <h6 class="fw-bold mb-2 text-cultural"><?php echo htmlspecialchars($relatedEvent->title); ?></h6>
                                    <div class="text-muted small mb-2">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo formatDate($relatedEvent->event_date); ?>
                                    </div>
                                    <div class="text-muted small mb-3">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo htmlspecialchars($relatedEvent->venue); ?>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="fw-bold text-primary fs-5"><?php echo formatCurrency($relatedEvent->price); ?></span>
                                        <a href="details.php?id=<?php echo $relatedEvent->id; ?>"
                                           class="btn btn-primary-modern btn-sm">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- Call to Action -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="text-center p-5" style="background: var(--earth-gradient); border-radius: var(--border-radius-lg); color: white;">
                        <h3 class="fw-bold mb-3">Ready to Join the Ubuntu Community?</h3>
                        <p class="lead mb-4">
                            Experience the spirit of togetherness through our cultural events.<br>
                            "I am because we are" - Ubuntu Philosophy
                        </p>
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="./" class="btn btn-accent-modern btn-lg">
                                <i class="fas fa-calendar-star me-2"></i>Browse All Events
                            </a>
                            <a href="search.php" class="btn btn-outline-modern">
                                <i class="fas fa-search me-2"></i>Search Events
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <!-- Event Details JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load cart count
            loadCartCount();

            // Navbar scroll effect
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar-modern');
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Update subtotal when quantity changes
            const quantitySelect = document.getElementById('quantity');
            const subtotalElement = document.querySelector('.subtotal');
            const pricePerTicket = <?php echo $event->price; ?>;

            if (quantitySelect && subtotalElement) {
                quantitySelect.addEventListener('change', function() {
                    const quantity = parseInt(this.value);
                    const subtotal = quantity * pricePerTicket;
                    subtotalElement.textContent = new Intl.NumberFormat('en-ZA', {
                        style: 'currency',
                        currency: 'ZAR'
                    }).format(subtotal);

                    // Update quick book link
                    const quickBookBtn = document.querySelector('.quick-book-btn');
                    if (quickBookBtn) {
                        const url = new URL(quickBookBtn.href);
                        url.searchParams.set('quantity', quantity);
                        quickBookBtn.href = url.toString();
                    }
                });
            }

            // Add to cart functionality
            const addToCartBtn = document.querySelector('.add-to-cart-btn');
            if (addToCartBtn) {
                addToCartBtn.addEventListener('click', function() {
                    const eventId = document.querySelector('.booking-form').dataset.eventId;
                    const quantity = document.getElementById('quantity').value;

                    // Show loading state
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding...';
                    this.disabled = true;

                    // Add to cart
                    addToCart(eventId, quantity).then(success => {
                        if (success) {
                            this.innerHTML = '<i class="fas fa-check me-2"></i>Added!';
                            setTimeout(() => {
                                this.innerHTML = originalText;
                                this.disabled = false;
                            }, 2000);

                            // Update cart count
                            loadCartCount();
                        } else {
                            this.innerHTML = '<i class="fas fa-times me-2"></i>Error';
                            setTimeout(() => {
                                this.innerHTML = originalText;
                                this.disabled = false;
                            }, 2000);
                        }
                    });
                });
            }

            // Animate related event cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'slideUp 0.6s ease-out';
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            const relatedCards = document.querySelectorAll('.related-event-card');
            relatedCards.forEach(card => {
                observer.observe(card);
            });

            // Add hover effects to meta items
            const metaItems = document.querySelectorAll('.event-meta-item');
            metaItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Social share tracking
            const socialBtns = document.querySelectorAll('.social-share-btn');
            socialBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const platform = this.textContent.trim();
                    console.log(`Shared on ${platform}`);
                });
            });
        });

        // Add to cart function
        async function addToCart(eventId, quantity) {
            try {
                const response = await fetch('../booking/add_to_cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `event_id=${eventId}&quantity=${quantity}`
                });

                const result = await response.json();
                return result.success;
            } catch (error) {
                console.error('Error adding to cart:', error);
                return false;
            }
        }

        // Load cart count function
        async function loadCartCount() {
            try {
                const response = await fetch('../booking/get_cart_count.php');
                const result = await response.json();

                if (result.success) {
                    const cartCountElement = document.querySelector('.cart-count');
                    if (result.cart_count > 0) {
                        cartCountElement.textContent = result.cart_count;
                        cartCountElement.style.display = 'block';
                    } else {
                        cartCountElement.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Failed to load cart count:', error);
            }
        }
    </script>
</body>
</html>
