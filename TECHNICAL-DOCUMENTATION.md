# Ukuqala Events - Technical Documentation

## 1. Project Overview

**System Name**: Ukuqala Events - Online Event Booking System  
**Version**: 1.0.0  
**Technology Stack**: PHP 8.0+, MySQL 8.0, Bootstrap 5, Docker  
**Architecture**: MVC Pattern with Modular Components  

### 1.1 Purpose
A comprehensive event booking platform enabling users to browse, book, and manage event tickets with administrative oversight and payment processing capabilities.

### 1.2 Key Features
- User authentication & role-based access
- Event management (CRUD operations)
- Shopping cart & booking system
- Payment processing simulation
- Admin dashboard with analytics
- Email notifications
- Responsive design

## 2. System Architecture

### 2.1 Architecture Pattern
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │    Business     │    │      Data       │
│     Layer       │◄──►│     Logic       │◄──►│     Layer       │
│   (Views/UI)    │    │   (Classes)     │    │   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 Directory Structure
```
ukuqala-events/
├── admin/              # Admin panel
├── auth/               # Authentication
├── booking/            # Booking system
├── events/             # Event pages
├── user/               # User dashboard
├── includes/           # Core files
│   ├── config.php      # Configuration
│   ├── functions.php   # Business logic
│   └── email.php       # Email handling
├── assets/             # Static resources
├── database/           # SQL schemas
└── docker/             # Docker configs
```

### 2.3 Core Components
- **Database Layer**: PDO-based database abstraction
- **Authentication**: Session-based with CSRF protection
- **Business Logic**: Object-oriented PHP classes
- **Email System**: PHPMailer integration
- **Security**: Input validation, SQL injection prevention

## 3. Database Design

### 3.1 Entity Relationship Diagram
```
Users (1) ──── (M) Bookings (M) ──── (1) Events
  │                 │
  │                 │
  └── (1) ──── (M) Cart
  │
  └── (1) ──── (M) Sessions
```

### 3.2 Core Tables

**Users Table**
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Events Table**
```sql
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    total_tickets INT NOT NULL,
    available_tickets INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active'
);
```

**Bookings Table**
```sql
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled'),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (event_id) REFERENCES events(id)
);
```

## 4. Implementation Details

### 4.1 Authentication System
```php
class UserManager {
    public function login($username, $password) {
        // Verify credentials using password_hash()
        // Set session variables
        // Return authentication status
    }
    
    public function register($userData) {
        // Validate input data
        // Hash password
        // Insert user record
    }
}
```

### 4.2 Booking System
```php
class BookingManager {
    public function createBooking($userId, $eventId, $quantity, $attendeeData) {
        // Check ticket availability
        // Generate booking reference
        // Create booking record
        // Update event availability
        // Return booking ID
    }
}
```

### 4.3 Cart Management
```php
class CartManager {
    public function addToCart($userId, $eventId, $quantity) {
        // Validate availability
        // Add/update cart item
        // Return success status
    }
    
    public function getCartItems($userId) {
        // Retrieve user's cart items
        // Join with event details
        // Return cart data
    }
}
```

### 4.4 Security Implementation
- **CSRF Protection**: Token-based form validation
- **SQL Injection Prevention**: Prepared statements
- **Input Sanitization**: htmlspecialchars(), trim()
- **Session Security**: Timeout, secure cookies
- **Password Security**: password_hash() with bcrypt

## 5. Deployment Guide

### 5.1 Docker Deployment (Recommended)
```bash
# Clone repository
git clone <repository-url>
cd ukuqala-events

# Start services
docker-compose up -d

# Access application
http://localhost:8000
```

### 5.2 Manual Deployment
```bash
# Requirements
- PHP 8.0+ with extensions: mysqli, json, curl
- MySQL 8.0+
- Web server (Apache/Nginx)

# Setup steps
1. Configure database connection in includes/config.php
2. Import database/schema.sql
3. Set file permissions (uploads directory)
4. Configure email settings
```

### 5.3 Environment Configuration
```php
// Production settings
define('DB_HOST', 'production-host');
define('SMTP_HOST', 'smtp.gmail.com');
define('ENVIRONMENT', 'production');

// Security settings
error_reporting(0);
ini_set('display_errors', 0);
```

## 6. API Documentation

### 6.1 Authentication Endpoints
```
POST /api/auth/login
Body: {"username": "user", "password": "pass"}
Response: {"success": true, "message": "Login successful"}

POST /api/auth/register
Body: {"username": "user", "email": "email", "password": "pass"}
Response: {"success": true, "user_id": 123}
```

### 6.2 Event Endpoints
```
GET /api/events
Response: {"success": true, "data": [...]}

GET /api/events/{id}
Response: {"success": true, "data": {...}}

POST /api/events (Admin only)
Body: {"title": "Event", "price": 100, ...}
Response: {"success": true, "event_id": 123}
```

### 6.3 Booking Endpoints
```
POST /api/bookings
Body: {"event_id": 1, "quantity": 2, "attendee_data": {...}}
Response: {"success": true, "booking_reference": "BK123456"}

GET /api/bookings (User bookings)
Response: {"success": true, "data": [...]}
```

## 7. User Manual

### 7.1 User Registration & Login
1. Navigate to `/auth/register.php`
2. Fill registration form with valid data
3. Login using credentials at `/auth/login.php`
4. Access user dashboard at `/user/dashboard.php`

### 7.2 Event Booking Process
1. Browse events at `/events/`
2. View event details and click "Add to Cart"
3. Review cart at `/booking/cart.php`
4. Proceed to checkout at `/booking/checkout.php`
5. Complete attendee information
6. Confirm booking and process payment

### 7.3 Admin Functions
1. Login with admin credentials
2. Access admin panel at `/admin/`
3. Manage events: Create, edit, delete
4. View bookings and generate reports
5. Monitor system statistics

### 7.4 Default Credentials
```
Admin Account:
- Email: <EMAIL>
- Password: password

Database:
- Host: localhost:3306
- Database: ukuqala_events
- Username: ukuqala_user
- Password: ukuqala_pass123
```

## 8. Testing & Quality Assurance

### 8.1 Testing Checklist
- [ ] User registration/login functionality
- [ ] Event browsing and search
- [ ] Cart operations (add, update, remove)
- [ ] Booking creation and confirmation
- [ ] Admin panel access and operations
- [ ] Email notification system
- [ ] Responsive design across devices
- [ ] Security measures (CSRF, SQL injection)

### 8.2 Performance Considerations
- Database indexing on frequently queried columns
- Image optimization for event photos
- Session cleanup for expired sessions
- Cart cleanup for abandoned items

## 9. Maintenance & Support

### 9.1 Regular Maintenance Tasks
- Database backup (daily recommended)
- Log file rotation and cleanup
- Security updates for dependencies
- Performance monitoring

### 9.2 Troubleshooting Common Issues
- **Database Connection**: Check config.php settings
- **Email Not Sending**: Verify SMTP configuration
- **Permission Errors**: Check file/directory permissions
- **Session Issues**: Clear browser cookies/cache

### 9.3 Monitoring & Logs
- Application logs: `logs/error.log`
- Database logs: MySQL error logs
- Web server logs: Apache/Nginx access logs

## 10. Future Enhancements

### 10.1 Planned Features
- Real payment gateway integration (Stripe, PayPal)
- QR code generation for tickets
- Mobile application development
- Advanced analytics and reporting
- Multi-language support
- Social media integration

### 10.2 Scalability Considerations
- Database sharding for large datasets
- CDN integration for static assets
- Load balancing for high traffic
- Caching mechanisms (Redis, Memcached)

---

**Document Version**: 1.0  
**Last Updated**: December 2024  
**Contact**: Development Team
