@echo off
REM Ukuqala Events - Quick Start Setup Script for Windows
REM This script automates the setup process for the Ukuqala Events application

echo.
echo 🎉 Welcome to Ukuqala Events Setup!
echo ==================================
echo.

REM Check if Docker is installed
echo [STEP] Checking Docker installation...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed. Please install Docker Desktop first.
    echo Visit: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Desktop first.
    echo Visit: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

echo [INFO] Docker and Docker Compose are installed ✓

REM Check if PHP is installed
echo [STEP] Checking PHP installation...
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] PHP is not installed. Please install PHP 8.0 or higher.
    echo Visit: https://www.php.net/downloads.php
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('php --version ^| findstr /r "^PHP"') do set PHP_VERSION=%%i
echo [INFO] PHP version %PHP_VERSION% detected ✓

REM Create Docker Compose file
echo [STEP] Creating Docker Compose configuration...

(
echo version: '3.8'
echo.
echo services:
echo   mysql:
echo     image: mysql:8.0
echo     container_name: ukuqala_mysql
echo     restart: always
echo     environment:
echo       MYSQL_ROOT_PASSWORD: rootpassword123
echo       MYSQL_DATABASE: ukuqala_events
echo       MYSQL_USER: ukuqala_user
echo       MYSQL_PASSWORD: ukuqala_pass123
echo     ports:
echo       - "3306:3306"
echo     volumes:
echo       - mysql_data:/var/lib/mysql
echo       - ./database:/docker-entrypoint-initdb.d
echo     command: --default-authentication-plugin=mysql_native_password
echo.
echo   phpmyadmin:
echo     image: phpmyadmin/phpmyadmin
echo     container_name: ukuqala_phpmyadmin
echo     restart: always
echo     ports:
echo       - "8080:80"
echo     environment:
echo       PMA_HOST: mysql
echo       PMA_USER: root
echo       PMA_PASSWORD: rootpassword123
echo     depends_on:
echo       - mysql
echo.
echo volumes:
echo   mysql_data:
) > docker-compose.yml

echo [INFO] Docker Compose file created ✓

REM Start Docker services
echo [STEP] Starting Docker services...
docker-compose down >nul 2>&1
docker-compose up -d

if %errorlevel% neq 0 (
    echo [ERROR] Failed to start Docker services
    pause
    exit /b 1
)

echo [INFO] Docker services started ✓
echo [INFO] MySQL will be available on port 3306
echo [INFO] phpMyAdmin will be available on http://localhost:8080

REM Wait for MySQL to be ready
echo [STEP] Waiting for MySQL to be ready...
timeout /t 30 /nobreak >nul

REM Create database directory and schema
echo [STEP] Setting up database schema...
if not exist "database" mkdir database

if not exist "database\schema.sql" (
    echo [WARNING] No schema.sql found. Creating basic schema...
    (
    echo -- Ukuqala Events Database Schema
    echo CREATE DATABASE IF NOT EXISTS ukuqala_events;
    echo USE ukuqala_events;
    echo.
    echo -- Users table
    echo CREATE TABLE IF NOT EXISTS users ^(
    echo     id INT AUTO_INCREMENT PRIMARY KEY,
    echo     first_name VARCHAR^(50^) NOT NULL,
    echo     last_name VARCHAR^(50^) NOT NULL,
    echo     email VARCHAR^(100^) UNIQUE NOT NULL,
    echo     password VARCHAR^(255^) NOT NULL,
    echo     phone VARCHAR^(20^),
    echo     role ENUM^('user', 'admin'^) DEFAULT 'user',
    echo     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    echo     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    echo ^);
    echo.
    echo -- Events table
    echo CREATE TABLE IF NOT EXISTS events ^(
    echo     id INT AUTO_INCREMENT PRIMARY KEY,
    echo     title VARCHAR^(200^) NOT NULL,
    echo     description TEXT,
    echo     event_date DATE NOT NULL,
    echo     event_time TIME NOT NULL,
    echo     venue VARCHAR^(200^) NOT NULL,
    echo     location VARCHAR^(200^) NOT NULL,
    echo     organizer VARCHAR^(100^) NOT NULL,
    echo     organizer_contact VARCHAR^(100^),
    echo     image_url VARCHAR^(500^),
    echo     price DECIMAL^(10,2^) NOT NULL DEFAULT 0.00,
    echo     total_tickets INT NOT NULL DEFAULT 0,
    echo     available_tickets INT NOT NULL DEFAULT 0,
    echo     category VARCHAR^(50^) NOT NULL,
    echo     status ENUM^('active', 'cancelled', 'draft'^) DEFAULT 'active',
    echo     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    echo     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    echo ^);
    echo.
    echo -- Bookings table
    echo CREATE TABLE IF NOT EXISTS bookings ^(
    echo     id INT AUTO_INCREMENT PRIMARY KEY,
    echo     user_id INT NOT NULL,
    echo     event_id INT NOT NULL,
    echo     quantity INT NOT NULL DEFAULT 1,
    echo     total_amount DECIMAL^(10,2^) NOT NULL,
    echo     booking_status ENUM^('pending', 'confirmed', 'cancelled'^) DEFAULT 'pending',
    echo     payment_status ENUM^('pending', 'completed', 'failed'^) DEFAULT 'pending',
    echo     booking_reference VARCHAR^(50^) UNIQUE NOT NULL,
    echo     attendee_name VARCHAR^(100^) NOT NULL,
    echo     attendee_email VARCHAR^(100^) NOT NULL,
    echo     attendee_phone VARCHAR^(20^),
    echo     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    echo     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    echo     FOREIGN KEY ^(user_id^) REFERENCES users^(id^) ON DELETE CASCADE,
    echo     FOREIGN KEY ^(event_id^) REFERENCES events^(id^) ON DELETE CASCADE
    echo ^);
    echo.
    echo -- Insert default admin user
    echo INSERT IGNORE INTO users ^(first_name, last_name, email, password, role^) 
    echo VALUES ^('Admin', 'User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'^);
    echo.
    echo -- Insert sample events
    echo INSERT IGNORE INTO events ^(title, description, event_date, event_time, venue, location, organizer, price, total_tickets, available_tickets, category^) VALUES
    echo ^('Ubuntu Music Festival', 'A celebration of African music and culture', '2024-12-25', '18:00:00', 'FNB Stadium', 'Johannesburg, South Africa', 'Ukuqala Events', 250.00, 1000, 1000, 'Concert'^),
    echo ^('Tech Innovation Summit', 'Leading technology conference in Africa', '2024-12-30', '09:00:00', 'Sandton Convention Centre', 'Sandton, South Africa', 'Ukuqala Events', 500.00, 500, 500, 'Conference'^);
    ) > database\schema.sql
)

REM Import schema
docker exec -i ukuqala_mysql mysql -u root -prootpassword123 < database\schema.sql

if %errorlevel% neq 0 (
    echo [WARNING] Database import may have failed. You can import manually via phpMyAdmin.
) else (
    echo [INFO] Database schema imported ✓
)

REM Create necessary directories
if not exist "uploads" mkdir uploads
if not exist "logs" mkdir logs

echo.
echo [INFO] 🚀 Setup completed successfully!
echo.
echo 📋 Access Information:
echo    • Application: http://localhost:8000
echo    • Admin Panel: http://localhost:8000/admin/
echo    • phpMyAdmin: http://localhost:8080
echo.
echo 🔑 Default Admin Credentials:
echo    • Email: <EMAIL>
echo    • Password: password
echo.
echo 🐳 Docker Services:
echo    • MySQL: localhost:3306
echo    • Database: ukuqala_events
echo    • User: ukuqala_user
echo    • Password: ukuqala_pass123
echo.

set /p start_server="Start PHP development server now? (y/n): "
if /i "%start_server%"=="y" (
    echo [INFO] Starting PHP server on http://localhost:8000
    echo [WARNING] Press Ctrl+C to stop the server
    php -S localhost:8000
) else (
    echo [INFO] You can start the server manually with: php -S localhost:8000
)

pause
