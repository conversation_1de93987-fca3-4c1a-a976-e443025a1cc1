# 🎉 Ukuqala Events - Event Booking System

A modern, feature-rich event booking and management system built with PHP and MySQL, featuring a sleek Spotify-inspired dark theme.

![Ukuqala Events](https://img.shields.io/badge/Version-1.0.0-green.svg)
![PHP](https://img.shields.io/badge/PHP-8.0+-blue.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)
![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)

## ✨ Features

### 🎯 Core Features
- **Event Management**: Create, edit, and manage events with detailed information
- **User Registration & Authentication**: Secure user accounts with role-based access
- **Booking System**: Complete booking workflow with cart functionality
- **Payment Integration**: Ready for payment gateway integration
- **Admin Dashboard**: Comprehensive admin panel with analytics and reports
- **Responsive Design**: Mobile-friendly interface with modern UI/UX

### 🎨 Design Features
- **Spotify-Inspired Dark Theme**: Modern, sleek dark interface
- **Interactive Charts**: Beautiful data visualization with Chart.js
- **Smooth Animations**: Polished user experience with CSS transitions
- **Consistent Branding**: Professional Ukuqala Events branding throughout

### 🔧 Technical Features
- **PHP 8.0+ Compatible**: Modern PHP with best practices
- **MySQL Database**: Robust relational database design
- **Docker Support**: Easy deployment with Docker containers
- **Security**: Password hashing, SQL injection prevention, XSS protection
- **Email Integration**: Booking confirmations and notifications

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

**For Windows:**
```cmd
# Simply double-click or run:
quick-start.bat
```

**For Linux/macOS:**
```bash
chmod +x quick-start.sh
./quick-start.sh
```

**📖 Windows Users**: See detailed Windows guide: [WINDOWS_SETUP.md](WINDOWS_SETUP.md)

### Option 2: Manual Setup

1. **Prerequisites**: PHP 8.0+, Docker, Docker Compose
2. **Clone/Download** the project files
3. **Follow the detailed setup guide**: [SETUP_GUIDE.md](SETUP_GUIDE.md)

## 📋 System Requirements

### Minimum Requirements
- **PHP**: 8.0 or higher
- **MySQL**: 8.0 or higher (via Docker)
- **RAM**: 2GB
- **Storage**: 1GB free space
- **Docker**: Latest version with Docker Compose

### Required PHP Extensions
- `mysqli` or `pdo-mysql`
- `json`
- `curl`
- `mbstring`
- `xml`

## 🌐 Access Points

After setup, access the application at:

- **Homepage**: `http://localhost:8000/`
- **Admin Panel**: `http://localhost:8000/admin/`
- **User Dashboard**: `http://localhost:8000/user/dashboard.php`
- **phpMyAdmin**: `http://localhost:8080`

## 🔑 Default Credentials

**Admin Account:**
- Email: `<EMAIL>`
- Password: `password`

**Database:**
- Host: `localhost:3306`
- Database: `ukuqala_events`
- Username: `ukuqala_user`
- Password: `ukuqala_pass123`

## 📁 Project Structure

```
ukuqala-events/
├── admin/                  # Admin panel pages
│   ├── index.php          # Admin dashboard
│   ├── events.php         # Event management
│   ├── bookings.php       # Booking management
│   └── reports.php        # Analytics & reports
├── assets/                 # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── images/            # Image assets
├── auth/                   # Authentication
│   ├── login.php          # User login
│   ├── register.php       # User registration
│   └── logout.php         # Logout functionality
├── booking/                # Booking system
│   ├── cart.php           # Shopping cart
│   ├── checkout.php       # Checkout process
│   └── confirmation.php   # Booking confirmation
├── database/               # Database files
│   └── schema.sql         # Database schema
├── events/                 # Event pages
│   ├── index.php          # Event listing
│   └── details.php        # Event details
├── includes/               # Core files
│   ├── config.php         # Configuration
│   ├── functions.php      # Helper functions
│   └── classes/           # PHP classes
├── user/                   # User dashboard
│   ├── dashboard.php      # User dashboard
│   └── profile.php        # User profile
├── index.php               # Homepage
├── SETUP_GUIDE.md         # Detailed setup instructions
├── quick-start.sh         # Linux/macOS setup script
├── quick-start.bat        # Windows setup script
└── docker-compose.yml     # Docker configuration
```

## 🔧 Key Features Implementation

### User Authentication System
- Secure password hashing using PHP's `password_hash()`
- Session-based authentication with timeout
- CSRF token protection for forms
- Role-based access control (user/admin)

### Event Management
- Complete CRUD operations for events
- Image upload and management
- Category-based organization
- Availability tracking

### Booking System
- Shopping cart functionality with session storage
- Real-time availability checking
- Transaction-safe booking process
- Booking reference generation

### Search and Filtering
- Full-text search across event titles and descriptions
- Location-based filtering
- Date range filtering
- Category-based filtering
- Pagination for large result sets

### Admin Panel
- Dashboard with key metrics
- Event management interface
- Booking oversight and management
- User management capabilities
- Report generation tools

## 🎨 Design Features

### Responsive Design
- Mobile-first approach using Bootstrap 5
- Flexible grid system for all screen sizes
- Touch-friendly interface elements
- Optimized images and loading

### User Experience
- Intuitive navigation structure
- Real-time feedback for user actions
- Loading states and progress indicators
- Error handling with user-friendly messages
- Accessibility considerations

### Visual Design
- Modern, clean interface
- Consistent color scheme and typography
- Card-based layout for content organization
- Icons from Font Awesome for visual clarity
- Gradient backgrounds and hover effects

## 🪟 Windows Users

### Quick Setup Options for Windows

**Option A: Docker Desktop (Recommended)**
```cmd
# 1. Install Docker Desktop for Windows
# 2. Run the automated setup
quick-start.bat
# 3. Access at http://localhost:8000
```

**Option B: XAMPP Stack**
```cmd
# 1. Install XAMPP with PHP 8.0+
# 2. Copy project to C:\xampp\htdocs\
# 3. Import database via phpMyAdmin
# 4. Access at http://localhost/ukuqala-events/
```

**📖 Detailed Windows Guide**: [WINDOWS_SETUP.md](WINDOWS_SETUP.md)

### Windows-Specific Features
- ✅ **Docker Desktop integration**
- ✅ **XAMPP/WAMP compatibility**
- ✅ **PowerShell and CMD support**
- ✅ **Automated setup script** (`quick-start.bat`)
- ✅ **WSL2 support** for better Docker performance

## 🔒 Security Features

- **Input Validation**: All user inputs are sanitized and validated
- **CSRF Protection**: Cross-site request forgery protection on all forms
- **SQL Injection Prevention**: Prepared statements for all database queries
- **Session Security**: Secure session handling with timeout
- **Password Security**: Strong password hashing and validation
- **Access Control**: Role-based permissions for different user types

## 📊 Database Schema

### Core Tables
- **users**: User accounts and profiles
- **events**: Event information and details
- **bookings**: Booking records and status
- **cart**: Temporary cart storage
- **user_sessions**: Session management

### Relationships
- Users can have multiple bookings
- Events can have multiple bookings
- Cart items link users to events
- Foreign key constraints ensure data integrity

## 🚀 Future Enhancements

- **Payment Integration**: Real payment gateway integration
- **Email Notifications**: Automated email confirmations and reminders
- **QR Code Generation**: Digital tickets with QR codes
- **Social Media Integration**: Event sharing and social login
- **Advanced Analytics**: Detailed reporting and analytics
- **Mobile App**: Native mobile application
- **Multi-language Support**: Internationalization
- **Event Reviews**: User reviews and ratings system

## 📝 Testing

### Manual Testing Checklist
- [ ] User registration and login
- [ ] Event browsing and search
- [ ] Cart functionality
- [ ] Checkout process
- [ ] Booking confirmation
- [ ] Admin panel access
- [ ] Event management
- [ ] Booking management
- [ ] Responsive design on various devices

### Test Accounts
- **Admin**: username: `admin`, password: `admin123`
- **User**: Register a new account or create test users

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is created for educational purposes as part of a web development course assignment.

## 👥 Credits

- **Bootstrap 5**: Frontend framework
- **Font Awesome**: Icons
- **jQuery**: JavaScript library
- **PHP**: Server-side scripting
- **MySQL**: Database management

## 📞 Support

For questions or issues related to this project, please refer to the documentation or contact the development team.

---

---

**🎉 Ready to get started? Run the quick-start script or follow the detailed setup guide!**

For questions or issues, please refer to the [SETUP_GUIDE.md](SETUP_GUIDE.md) troubleshooting section.
