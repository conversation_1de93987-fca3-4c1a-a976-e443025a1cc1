@echo off
echo Starting Ukuqala Events Application...
echo =====================================

REM Check if Docker is running
echo [STEP] Checking Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not installed or not running
    echo Please make sure Docker Desktop is installed and running
    pause
    exit /b 1
)
echo [INFO] Docker is available ✓

REM Stop any existing containers
echo [STEP] Stopping any existing containers...
docker-compose down >nul 2>&1

REM Build and start all services
echo [STEP] Building and starting all services...
docker-compose up -d --build
if %errorlevel% neq 0 (
    echo [ERROR] Failed to start services
    echo Please check Docker Desktop and try again
    pause
    exit /b 1
)
echo [INFO] All services started ✓

REM Wait for services to be ready
echo [STEP] Waiting for services to be ready...
timeout /t 30 /nobreak >nul

REM Check if MySQL is responding
echo [STEP] Testing MySQL connection...
docker exec event_booking_mysql mysqladmin ping -h localhost -u event_user -pevent_password >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] MySQL may still be starting up. This is normal for the first run.
    echo [INFO] The application will be available once MySQL is fully ready.
)

echo.
echo ✅ Setup completed successfully!
echo.
echo 📋 Access Information:
echo    • Application: http://localhost:8000
echo    • phpMyAdmin: http://localhost:8081
echo    • MySQL: localhost:3307 (for external connections)
echo.
echo 🔑 Database Credentials:
echo    • Database: event_booking_system
echo    • User: event_user
echo    • Password: event_password
echo.
echo 🐳 Docker Services Status:
docker-compose ps

echo.
echo [INFO] All services are running in Docker containers
echo [INFO] You can stop all services with: docker-compose down
echo [INFO] To view logs: docker-compose logs -f

pause
