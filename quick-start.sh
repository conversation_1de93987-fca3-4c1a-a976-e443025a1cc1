#!/bin/bash

# Ukuqala Events - Quick Start Setup Script
# This script automates the setup process for the Ukuqala Events application

set -e  # Exit on any error

echo "🎉 Welcome to Ukuqala Events Setup!"
echo "=================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    print_step "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed ✓"
}

# Check if PHP is installed
check_php() {
    print_step "Checking PHP installation..."
    if ! command -v php &> /dev/null; then
        print_error "PHP is not installed. Please install PHP 8.0 or higher."
        exit 1
    fi
    
    PHP_VERSION=$(php -v | head -n 1 | cut -d " " -f 2 | cut -d "." -f 1,2)
    print_status "PHP version $PHP_VERSION detected ✓"
    
    # Check required PHP extensions
    print_step "Checking PHP extensions..."
    REQUIRED_EXTENSIONS=("mysqli" "json" "curl" "mbstring")
    
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if php -m | grep -q "^$ext$"; then
            print_status "PHP extension $ext is installed ✓"
        else
            print_warning "PHP extension $ext is not installed"
        fi
    done
}

# Create Docker Compose file
create_docker_compose() {
    print_step "Creating Docker Compose configuration..."
    
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: ukuqala_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword123
      MYSQL_DATABASE: ukuqala_events
      MYSQL_USER: ukuqala_user
      MYSQL_PASSWORD: ukuqala_pass123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: ukuqala_phpmyadmin
    restart: always
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: rootpassword123
    depends_on:
      - mysql

volumes:
  mysql_data:
EOF
    
    print_status "Docker Compose file created ✓"
}

# Start Docker services
start_docker_services() {
    print_step "Starting Docker services..."
    
    # Stop any existing containers
    docker-compose down 2>/dev/null || true
    
    # Start services
    docker-compose up -d
    
    print_status "Docker services started ✓"
    print_status "MySQL will be available on port 3306"
    print_status "phpMyAdmin will be available on http://localhost:8080"
}

# Wait for MySQL to be ready
wait_for_mysql() {
    print_step "Waiting for MySQL to be ready..."
    
    # Wait up to 60 seconds for MySQL to be ready
    for i in {1..60}; do
        if docker exec ukuqala_mysql mysqladmin ping -h localhost -u root -prootpassword123 &> /dev/null; then
            print_status "MySQL is ready ✓"
            return 0
        fi
        echo -n "."
        sleep 1
    done
    
    print_error "MySQL failed to start within 60 seconds"
    exit 1
}

# Create database schema
setup_database() {
    print_step "Setting up database schema..."
    
    # Create database directory if it doesn't exist
    mkdir -p database
    
    # Create basic schema if it doesn't exist
    if [ ! -f "database/schema.sql" ]; then
        print_warning "No schema.sql found. Creating basic schema..."
        cat > database/schema.sql << 'EOF'
-- Ukuqala Events Database Schema
CREATE DATABASE IF NOT EXISTS ukuqala_events;
USE ukuqala_events;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Events table
CREATE TABLE IF NOT EXISTS events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    organizer_contact VARCHAR(100),
    image_url VARCHAR(500),
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_tickets INT NOT NULL DEFAULT 0,
    available_tickets INT NOT NULL DEFAULT 0,
    category VARCHAR(50) NOT NULL,
    status ENUM('active', 'cancelled', 'draft') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    booking_reference VARCHAR(50) UNIQUE NOT NULL,
    attendee_name VARCHAR(100) NOT NULL,
    attendee_email VARCHAR(100) NOT NULL,
    attendee_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT IGNORE INTO users (first_name, last_name, email, password, role) 
VALUES ('Admin', 'User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert sample events
INSERT IGNORE INTO events (title, description, event_date, event_time, venue, location, organizer, price, total_tickets, available_tickets, category) VALUES
('Ubuntu Music Festival', 'A celebration of African music and culture', '2024-12-25', '18:00:00', 'FNB Stadium', 'Johannesburg, South Africa', 'Ukuqala Events', 250.00, 1000, 1000, 'Concert'),
('Tech Innovation Summit', 'Leading technology conference in Africa', '2024-12-30', '09:00:00', 'Sandton Convention Centre', 'Sandton, South Africa', 'Ukuqala Events', 500.00, 500, 500, 'Conference');
EOF
    fi
    
    # Import schema
    docker exec -i ukuqala_mysql mysql -u root -prootpassword123 < database/schema.sql
    
    print_status "Database schema imported ✓"
}

# Set file permissions
set_permissions() {
    print_step "Setting file permissions..."
    
    # Create necessary directories
    mkdir -p uploads logs
    
    # Set permissions (Linux/macOS)
    if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "win32" ]]; then
        chmod -R 755 .
        chmod -R 777 uploads logs 2>/dev/null || true
        print_status "File permissions set ✓"
    else
        print_status "Skipping file permissions (Windows detected) ✓"
    fi
}

# Start PHP development server
start_php_server() {
    print_step "Starting PHP development server..."
    
    echo ""
    print_status "🚀 Setup completed successfully!"
    echo ""
    echo "📋 Access Information:"
    echo "   • Application: http://localhost:8000"
    echo "   • Admin Panel: http://localhost:8000/admin/"
    echo "   • phpMyAdmin: http://localhost:8080"
    echo ""
    echo "🔑 Default Admin Credentials:"
    echo "   • Email: <EMAIL>"
    echo "   • Password: password"
    echo ""
    echo "🐳 Docker Services:"
    echo "   • MySQL: localhost:3306"
    echo "   • Database: ukuqala_events"
    echo "   • User: ukuqala_user"
    echo "   • Password: ukuqala_pass123"
    echo ""
    
    read -p "Start PHP development server now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Starting PHP server on http://localhost:8000"
        print_warning "Press Ctrl+C to stop the server"
        php -S localhost:8000
    else
        print_status "You can start the server manually with: php -S localhost:8000"
    fi
}

# Main execution
main() {
    echo "Starting automated setup..."
    echo ""
    
    check_docker
    check_php
    create_docker_compose
    start_docker_services
    wait_for_mysql
    setup_database
    set_permissions
    start_php_server
}

# Run main function
main "$@"
