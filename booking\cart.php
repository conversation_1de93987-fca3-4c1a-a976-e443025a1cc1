<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('../auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

$pageTitle = 'Shopping Cart';
$userId = $_SESSION['user_id'];

// Handle cart actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'update':
            $eventId = $_POST['event_id'] ?? 0;
            $quantity = (int)($_POST['quantity'] ?? 0);

            if ($eventId && $quantity >= 0) {
                if ($cartManager->updateCartItem($userId, $eventId, $quantity)) {
                    setFlashMessage('success', 'Cart updated successfully!');
                } else {
                    setFlashMessage('error', 'Failed to update cart item.');
                }
            }
            break;

        case 'remove':
            $eventId = $_POST['event_id'] ?? 0;

            if ($eventId) {
                if ($cartManager->removeFromCart($userId, $eventId)) {
                    setFlashMessage('success', 'Item removed from cart!');
                } else {
                    setFlashMessage('error', 'Failed to remove item from cart.');
                }
            }
            break;

        case 'clear':
            if ($cartManager->clearCart($userId)) {
                setFlashMessage('success', 'Cart cleared successfully!');
            } else {
                setFlashMessage('error', 'Failed to clear cart.');
            }
            break;
    }

    redirect('cart.php');
}

// Get cart items
$cartItems = $cartManager->getCartItems($userId);
$cartTotal = $cartManager->getCartTotal($userId);
$cartCount = $cartManager->getCartCount($userId);

$flashMessage = getFlashMessage();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- African-Inspired Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&family=Playfair+Display:wght@400;500;600;700;800&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="../assets/css/modern-ui.css" rel="stylesheet">

    <style>
        body {
            background: var(--bg-primary);
            min-height: 100vh;
        }

        .main-content {
            padding-top: 120px;
            padding-bottom: 60px;
        }

        .cart-hero {
            background: var(--bg-secondary);
            color: var(--text-primary);
            padding: 4rem 0 3rem;
            margin-bottom: 3rem;
            border-radius: var(--border-radius-lg);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--bg-elevated);
        }

        .cart-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--pattern-tribal);
            opacity: 0.1;
        }

        .cart-hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .cart-progress {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 3rem;
            box-shadow: var(--shadow-cultural);
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
        }

        .step-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(212, 165, 116, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--text-secondary);
            border: 3px solid transparent;
            transition: var(--transition-cultural);
        }

        .step-number.active {
            background: var(--sunset-gradient);
            color: white;
            border-color: white;
            box-shadow: var(--shadow-cultural);
            transform: scale(1.1);
        }

        .progress-connector {
            flex: 1;
            height: 3px;
            background: rgba(212, 165, 116, 0.3);
            border-radius: 2px;
            margin: 0 1rem;
        }

        .progress-connector.active {
            background: var(--primary-gradient);
        }

        .cart-item-card {
            background: var(--bg-tertiary);
            border: 1px solid var(--bg-elevated);
            border-radius: var(--border-radius-lg);
            margin-bottom: 1.5rem;
            transition: all 0.2s ease;
            overflow: hidden;
            position: relative;
            color: var(--text-primary);
        }

        .cart-item-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
        }

        .cart-item-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-strong);
            border-color: var(--accent-color);
        }

        .cart-item-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: var(--border-radius);
            transition: var(--transition-cultural);
        }

        .cart-item-card:hover .cart-item-image {
            transform: scale(1.05);
        }

        .quantity-selector {
            background: white;
            border: 3px solid var(--primary-color);
            border-radius: var(--border-radius);
            padding: 0.8rem 1rem;
            font-weight: 600;
            color: var(--text-primary);
            transition: var(--transition-cultural);
        }

        .quantity-selector:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.15);
            outline: none;
        }

        .cart-summary-card {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: 2.5rem;
            box-shadow: var(--shadow-cultural);
            position: sticky;
            top: 120px;
            border: 3px solid var(--accent-color);
        }

        .empty-cart-container {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            border: 2px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            padding: 4rem 2rem;
            text-align: center;
            box-shadow: var(--shadow-cultural);
        }

        .empty-cart-icon {
            font-size: 5rem;
            color: var(--primary-color);
            margin-bottom: 2rem;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
        }

        .category-badge {
            background: var(--sunset-gradient);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition-cultural);
            display: inline-block;
            margin: 0.5rem;
            border: 2px solid transparent;
        }

        .category-badge:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-soft);
            border-color: white;
            color: white;
        }

        .help-card {
            background: var(--forest-gradient);
            color: white;
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: var(--shadow-cultural);
        }

        .security-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-top: 2rem;
            text-align: center;
        }

        .item-meta {
            color: var(--text-secondary);
            font-size: 0.95rem;
            margin-bottom: 0.5rem;
        }

        .item-meta i {
            color: var(--accent-color);
            margin-right: 0.5rem;
            width: 16px;
        }

        .price-display {
            font-family: var(--font-display);
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .total-price {
            font-family: var(--font-display);
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../">
                <i class="fas fa-drum me-2"></i>
                Ukuqala Events
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../events/search.php">Search</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative active" href="cart.php">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle"
                                  <?php echo $cartCount > 0 ? '' : 'style="display: none;"'; ?>>
                                <?php echo $cartCount; ?>
                            </span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($_SESSION['first_name'] ?? 'User'); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../user/dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">
                                <i class="fas fa-user-edit me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="../user/bookings.php">
                                <i class="fas fa-ticket-alt me-2"></i>My Bookings
                            </a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../admin/">
                                    <i class="fas fa-cog me-2"></i>Admin Panel
                                </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Cart Hero Section -->
            <div class="cart-hero">
                <div class="cart-hero-content">
                    <h1 class="display-3 fw-bold mb-3">
                        <i class="fas fa-shopping-basket me-3"></i>
                        Your Event Cart
                    </h1>
                    <p class="lead mb-0">
                        <?php if ($cartCount > 0): ?>
                            You have <?php echo $cartCount; ?> amazing event<?php echo $cartCount > 1 ? 's' : ''; ?> ready to book!
                        <?php else: ?>
                            Discover incredible events and create unforgettable memories
                        <?php endif; ?>
                    </p>
                </div>
            </div>

            <!-- Shopping Progress -->
            <?php if (!empty($cartItems)): ?>
                <div class="cart-progress">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="progress-step">
                                <div class="step-number active">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">Review Cart</h6>
                                    <small class="text-muted">Check your selected events</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 d-none d-md-block">
                            <div class="progress-connector"></div>
                        </div>
                        <div class="col-md-3">
                            <div class="progress-step">
                                <div class="step-number">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">Checkout</h6>
                                    <small class="text-muted">Enter payment details</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1 d-none d-md-block">
                            <div class="progress-connector"></div>
                        </div>
                        <div class="col-md-3">
                            <div class="progress-step">
                                <div class="step-number">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">Confirmation</h6>
                                    <small class="text-muted">Complete your booking</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($flashMessage): ?>
                <div class="alert alert-<?php echo $flashMessage['type'] === 'error' ? 'danger' : $flashMessage['type']; ?> alert-dismissible fade show">
                    <?php echo $flashMessage['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8 mb-4">
                    <?php if (!empty($cartItems)): ?>
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3 class="section-title">
                                <i class="fas fa-list-ul"></i>
                                Your Selected Events
                            </h3>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="clear">
                                <button type="submit" class="btn btn-outline-modern"
                                        onclick="return confirm('Are you sure you want to clear your cart?')">
                                    <i class="fas fa-trash me-2"></i>Clear All
                                </button>
                            </form>
                        </div>

                        <?php foreach ($cartItems as $item): ?>
                            <div class="cart-item-card">
                                <div class="row align-items-center p-3">
                                    <div class="col-md-3">
                                        <img src="<?php echo $item->image_url ?: '../images/africa1.png'; ?>"
                                             alt="<?php echo htmlspecialchars($item->title); ?>"
                                             class="cart-item-image">
                                    </div>
                                    <div class="col-md-4">
                                        <h5 class="fw-bold mb-2 text-cultural">
                                            <?php echo htmlspecialchars($item->title); ?>
                                        </h5>
                                        <div class="item-meta">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?php echo formatDate($item->event_date); ?>
                                        </div>
                                        <div class="item-meta">
                                            <i class="fas fa-clock"></i>
                                            <?php echo formatTime($item->event_time); ?>
                                        </div>
                                        <div class="item-meta">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <?php echo htmlspecialchars($item->venue); ?>
                                        </div>
                                        <div class="item-meta">
                                            <i class="fas fa-tag"></i>
                                            <?php echo htmlspecialchars($item->category); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <div class="price-display mb-1">
                                            <?php echo formatCurrency($item->price); ?>
                                        </div>
                                        <small class="text-muted">per ticket</small>
                                    </div>
                                    <div class="col-md-2">
                                        <form method="POST" class="quantity-form">
                                            <input type="hidden" name="action" value="update">
                                            <input type="hidden" name="event_id" value="<?php echo $item->event_id; ?>">
                                            <label class="form-label small fw-bold">Quantity:</label>
                                            <select name="quantity" class="quantity-selector w-100"
                                                    onchange="this.form.submit()">
                                                <?php for ($i = 1; $i <= min(10, $item->available_tickets); $i++): ?>
                                                    <option value="<?php echo $i; ?>"
                                                            <?php echo $i == $item->quantity ? 'selected' : ''; ?>>
                                                        <?php echo $i; ?> ticket<?php echo $i > 1 ? 's' : ''; ?>
                                                    </option>
                                                <?php endfor; ?>
                                            </select>
                                        </form>
                                    </div>
                                    <div class="col-md-1 text-center">
                                        <div class="total-price mb-2">
                                            <?php echo formatCurrency($item->price * $item->quantity); ?>
                                        </div>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="remove">
                                            <input type="hidden" name="event_id" value="<?php echo $item->event_id; ?>">
                                            <button type="submit" class="btn btn-outline-modern btn-sm"
                                                    onclick="return confirm('Remove this event from your cart?')"
                                                    title="Remove from cart">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="empty-cart-container">
                            <div class="empty-cart-icon">
                                <i class="fas fa-drum"></i>
                            </div>
                            <h2 class="display-4 fw-bold mb-3 text-cultural">
                                Your Event Journey Awaits!
                            </h2>
                            <p class="lead text-muted mb-4">
                                Discover incredible African-inspired events and cultural experiences.<br>
                                Start building your perfect event collection today!
                            </p>

                            <div class="d-grid gap-3 d-md-flex justify-content-md-center mb-5">
                                <a href="../events/" class="btn btn-primary-modern btn-lg">
                                    <i class="fas fa-calendar-star me-2"></i>Explore Events
                                </a>
                                <a href="../events/search.php" class="btn btn-accent-modern btn-lg">
                                    <i class="fas fa-search me-2"></i>Search Events
                                </a>
                            </div>

                            <!-- Popular Categories -->
                            <div class="mt-5">
                                <h5 class="fw-bold mb-4 text-cultural">Popular Event Categories</h5>
                                <div class="d-flex flex-wrap justify-content-center">
                                    <a href="../events/?category=music" class="category-badge">
                                        <i class="fas fa-music me-2"></i>Music & Concerts
                                    </a>
                                    <a href="../events/?category=cultural" class="category-badge">
                                        <i class="fas fa-masks-theater me-2"></i>Cultural Events
                                    </a>
                                    <a href="../events/?category=business" class="category-badge">
                                        <i class="fas fa-briefcase me-2"></i>Business & Networking
                                    </a>
                                    <a href="../events/?category=workshop" class="category-badge">
                                        <i class="fas fa-graduation-cap me-2"></i>Workshops & Learning
                                    </a>
                                    <a href="../events/?category=entertainment" class="category-badge">
                                        <i class="fas fa-theater-masks me-2"></i>Entertainment
                                    </a>
                                    <a href="../events/?category=sports" class="category-badge">
                                        <i class="fas fa-running me-2"></i>Sports & Fitness
                                    </a>
                                </div>
                            </div>

                            <!-- Inspiration Quote -->
                            <div class="mt-5 pt-4 border-top">
                                <blockquote class="blockquote text-center">
                                    <p class="mb-3 fst-italic text-muted">
                                        "Ubuntu: I am because we are. Every event is a chance to connect and celebrate together."
                                    </p>
                                    <footer class="blockquote-footer text-muted">
                                        African Philosophy
                                    </footer>
                                </blockquote>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Cart Summary -->
                <div class="col-lg-4">
                    <?php if (!empty($cartItems)): ?>
                        <div class="cart-summary-card">
                            <div class="text-center mb-4">
                                <h4 class="fw-bold mb-2">
                                    <i class="fas fa-receipt me-2"></i>
                                    Order Summary
                                </h4>
                                <p class="mb-0 opacity-90">Review your event selection</p>
                            </div>

                            <div class="summary-breakdown">
                                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom border-light">
                                    <span class="fw-bold">
                                        <i class="fas fa-ticket-alt me-2"></i>
                                        Events (<?php echo $cartCount; ?>):
                                    </span>
                                    <span class="fw-bold"><?php echo formatCurrency($cartTotal); ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom border-light">
                                    <span>
                                        <i class="fas fa-cog me-2"></i>
                                        Service Fee:
                                    </span>
                                    <span class="text-success fw-bold">FREE</span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom border-light">
                                    <span>
                                        <i class="fas fa-percentage me-2"></i>
                                        Taxes & Fees:
                                    </span>
                                    <span>Included</span>
                                </div>

                                <div class="total-section text-center mb-4">
                                    <h3 class="fw-bold mb-1">
                                        Total: <?php echo formatCurrency($cartTotal); ?>
                                    </h3>
                                    <small class="opacity-75">All fees included</small>
                                </div>
                            </div>

                            <div class="d-grid gap-3">
                                <a href="checkout.php" class="btn btn-accent-modern btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>
                                    Secure Checkout
                                </a>
                                <a href="../events/" class="btn btn-secondary-modern">
                                    <i class="fas fa-plus me-2"></i>
                                    Add More Events
                                </a>
                            </div>

                            <!-- Security Info -->
                            <div class="security-info">
                                <div class="text-center">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    <small>256-bit SSL Encryption</small>
                                </div>
                                <div class="text-center mt-2">
                                    <i class="fas fa-lock me-2"></i>
                                    <small>Secure Payment Processing</small>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Help & Support -->
                    <div class="help-card">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-hands-helping me-2"></i>
                            Need Assistance?
                        </h5>
                        <p class="mb-3">Our Ubuntu spirit means we're here to help you every step of the way!</p>
                        <div class="d-grid gap-2">
                            <a href="../events/search.php" class="btn btn-outline-modern">
                                <i class="fas fa-search me-2"></i>Find More Events
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-modern">
                                <i class="fas fa-envelope me-2"></i>Contact Support
                            </a>
                            <a href="tel:+27123456789" class="btn btn-outline-modern">
                                <i class="fas fa-phone me-2"></i>Call Us Now
                            </a>
                        </div>

                        <!-- Quick Tips -->
                        <div class="mt-4 pt-3 border-top border-light">
                            <h6 class="fw-bold mb-2">Quick Tips:</h6>
                            <ul class="list-unstyled small">
                                <li class="mb-1"><i class="fas fa-check me-2"></i>Book early for better prices</li>
                                <li class="mb-1"><i class="fas fa-check me-2"></i>Check event requirements</li>
                                <li class="mb-1"><i class="fas fa-check me-2"></i>Save events to your wishlist</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="../assets/js/modern-app.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced quantity form handling
            const quantityForms = document.querySelectorAll('.quantity-form');
            quantityForms.forEach(form => {
                const select = form.querySelector('select[name="quantity"]');
                if (select) {
                    select.addEventListener('change', function() {
                        // Add loading state
                        select.disabled = true;
                        select.style.opacity = '0.6';

                        // Show loading indicator
                        const loadingSpinner = document.createElement('div');
                        loadingSpinner.className = 'loading-spinner-modern';
                        loadingSpinner.style.width = '20px';
                        loadingSpinner.style.height = '20px';
                        loadingSpinner.style.position = 'absolute';
                        loadingSpinner.style.top = '50%';
                        loadingSpinner.style.right = '10px';
                        loadingSpinner.style.transform = 'translateY(-50%)';

                        const container = select.parentElement;
                        container.style.position = 'relative';
                        container.appendChild(loadingSpinner);

                        setTimeout(() => {
                            form.submit();
                        }, 300);
                    });
                }
            });

            // Smooth scroll to cart items when page loads with items
            <?php if (!empty($cartItems)): ?>
            const cartItems = document.querySelector('.cart-item-card');
            if (cartItems) {
                setTimeout(() => {
                    cartItems.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }, 500);
            }
            <?php endif; ?>

            // Add hover effects to cart items
            const cartItemCards = document.querySelectorAll('.cart-item-card');
            cartItemCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Enhanced remove button confirmation
            const removeButtons = document.querySelectorAll('button[onclick*="Remove"]');
            removeButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Create custom confirmation modal
                    const eventTitle = this.closest('.cart-item-card').querySelector('h5').textContent;

                    if (confirm(`Are you sure you want to remove "${eventTitle}" from your cart?\n\nThis action cannot be undone.`)) {
                        this.closest('form').submit();
                    }
                });
            });

            // Add animation to empty cart icon
            const emptyCartIcon = document.querySelector('.empty-cart-icon i');
            if (emptyCartIcon) {
                setInterval(() => {
                    emptyCartIcon.style.transform = 'rotate(360deg)';
                    setTimeout(() => {
                        emptyCartIcon.style.transform = 'rotate(0deg)';
                    }, 1000);
                }, 5000);
            }

            // Navbar scroll effect
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar-modern');
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });
        });
    </script>
</body>
</html>
